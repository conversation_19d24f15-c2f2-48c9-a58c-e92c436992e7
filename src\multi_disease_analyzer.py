#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多疾病关联分析模块
Multi-Disease Analysis Module

负责分析多种疾病的关联性和综合风险评估
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, roc_auc_score
import itertools
import logging
import os

logger = logging.getLogger(__name__)

class MultiDiseaseAnalyzer:
    """多疾病分析类"""
    
    def __init__(self):
        """初始化多疾病分析器"""
        self.common_features = []
        self.disease_correlations = {}
        self.multi_disease_models = {}
        self.risk_profiles = {}
        
    def analyze_multi_disease_risk(self, processed_data, model_results):
        """分析多疾病风险"""
        logger.info("开始多疾病关联分析")
        
        # 1. 识别共同特征
        common_features = self._identify_common_features(processed_data)
        
        # 2. 创建统一数据集
        unified_dataset = self._create_unified_dataset(processed_data, common_features)
        
        # 3. 分析疾病间相关性
        disease_correlations = self._analyze_disease_correlations(unified_dataset)
        
        # 4. 构建多疾病预测模型
        multi_disease_models = self._build_multi_disease_models(unified_dataset)
        
        # 5. 风险分层分析
        risk_profiles = self._analyze_risk_profiles(unified_dataset)
        
        # 6. 共病模式分析
        comorbidity_patterns = self._analyze_comorbidity_patterns(unified_dataset)
        
        results = {
            'common_features': common_features,
            'disease_correlations': disease_correlations,
            'multi_disease_models': multi_disease_models,
            'risk_profiles': risk_profiles,
            'comorbidity_patterns': comorbidity_patterns,
            'unified_dataset': unified_dataset
        }
        
        # 保存分析结果
        self._save_multi_disease_results(results)
        
        return results
    
    def _identify_common_features(self, processed_data):
        """识别各疾病数据集的共同特征"""
        logger.info("识别共同特征")
        
        # 获取所有数据集的特征列表
        feature_sets = []
        for disease_name, df in processed_data.items():
            # 排除目标变量和ID列
            exclude_cols = ['id', 'ID', 'HeartDisease', 'stroke', 'death_risk', 'Status', 
                          'Status_encoded', 'poor_outcome', 'N_Days']
            features = [col for col in df.columns if col not in exclude_cols]
            feature_sets.append(set(features))
        
        # 找到所有数据集的交集特征
        if feature_sets:
            common_features = set.intersection(*feature_sets)
        else:
            common_features = set()
        
        # 手动添加一些重要的共同特征（基于医学知识）
        potential_common = {
            'age', 'Age', 'Age_years',  # 年龄
            'sex_encoded', 'Sex_encoded', 'gender_encoded',  # 性别
        }
        
        # 检查哪些潜在共同特征实际存在
        actual_common = []
        for disease_name, df in processed_data.items():
            for feature in df.columns:
                if any(common in feature.lower() for common in ['age', 'sex', 'gender']):
                    if feature not in actual_common:
                        actual_common.append(feature)
        
        logger.info(f"识别到的共同特征: {list(common_features)}")
        logger.info(f"潜在相关特征: {actual_common}")
        
        return {
            'strict_common': list(common_features),
            'related_features': actual_common
        }
    
    def _create_unified_dataset(self, processed_data, common_features):
        """创建统一的多疾病数据集"""
        logger.info("创建统一数据集")
        
        unified_data = []
        
        for disease_name, df in processed_data.items():
            # 获取目标变量
            target_col = self._get_target_column(disease_name)
            if target_col not in df.columns:
                continue
            
            # 创建疾病特异性数据
            disease_data = df.copy()
            
            # 标准化特征名称
            disease_data = self._standardize_feature_names(disease_data, disease_name)
            
            # 添加疾病标识
            disease_data['disease_type'] = disease_name
            disease_data[f'{disease_name}_risk'] = disease_data[target_col]
            
            # 选择相关特征
            relevant_features = self._select_relevant_features(disease_data, disease_name)
            
            unified_data.append(disease_data[relevant_features + ['disease_type', f'{disease_name}_risk']])
        
        if not unified_data:
            logger.error("无法创建统一数据集")
            return pd.DataFrame()
        
        # 合并数据集
        unified_df = pd.concat(unified_data, ignore_index=True, sort=False)
        
        # 填充缺失值
        unified_df = unified_df.fillna(0)
        
        logger.info(f"统一数据集形状: {unified_df.shape}")
        return unified_df
    
    def _standardize_feature_names(self, df, disease_name):
        """标准化特征名称"""
        df = df.copy()
        
        # 年龄标准化
        if disease_name == 'heart' and 'Age' in df.columns:
            df['age_std'] = df['Age']
        elif disease_name == 'stroke' and 'age' in df.columns:
            df['age_std'] = df['age']
        elif disease_name == 'cirrhosis' and 'Age_years' in df.columns:
            df['age_std'] = df['Age_years']
        elif disease_name == 'cirrhosis' and 'Age' in df.columns:
            df['age_std'] = df['Age'] / 365.25
        
        # 性别标准化
        if disease_name == 'heart' and 'Sex_encoded' in df.columns:
            df['sex_std'] = df['Sex_encoded']
        elif disease_name == 'stroke' and 'gender_encoded' in df.columns:
            df['sex_std'] = df['gender_encoded']
        elif disease_name == 'cirrhosis' and 'Sex_encoded' in df.columns:
            df['sex_std'] = df['Sex_encoded']
        
        return df
    
    def _select_relevant_features(self, df, disease_name):
        """选择相关特征"""
        base_features = ['age_std', 'sex_std']
        
        # 疾病特异性特征
        disease_specific = {
            'heart': ['RestingBP', 'Cholesterol', 'MaxHR', 'FastingBS', 'ExerciseAngina_encoded'],
            'stroke': ['hypertension', 'heart_disease', 'avg_glucose_level', 'bmi', 'smoking_encoded'],
            'cirrhosis': ['Bilirubin', 'Albumin', 'Ascites_encoded', 'Hepatomegaly_encoded', 'Stage']
        }
        
        specific_features = disease_specific.get(disease_name, [])
        all_features = base_features + specific_features
        
        # 只保留存在的特征
        available_features = [f for f in all_features if f in df.columns]
        
        return available_features
    
    def _analyze_disease_correlations(self, unified_df):
        """分析疾病间相关性"""
        logger.info("分析疾病间相关性")
        
        if unified_df.empty:
            return {}
        
        # 创建疾病风险矩阵
        diseases = ['heart', 'stroke', 'cirrhosis']
        risk_columns = [f'{disease}_risk' for disease in diseases if f'{disease}_risk' in unified_df.columns]
        
        if len(risk_columns) < 2:
            logger.warning("可用疾病风险列不足，无法计算相关性")
            return {}
        
        # 计算相关性矩阵
        correlation_matrix = unified_df[risk_columns].corr()
        
        # 计算共病概率
        comorbidity_stats = {}
        for i, disease1 in enumerate(risk_columns):
            for j, disease2 in enumerate(risk_columns):
                if i < j:  # 避免重复计算
                    # 计算同时患两种疾病的概率
                    both_diseases = ((unified_df[disease1] == 1) & (unified_df[disease2] == 1)).sum()
                    total_patients = len(unified_df)
                    prob_both = both_diseases / total_patients if total_patients > 0 else 0
                    
                    # 计算条件概率
                    disease1_patients = (unified_df[disease1] == 1).sum()
                    disease2_patients = (unified_df[disease2] == 1).sum()
                    
                    prob_disease2_given_disease1 = (both_diseases / disease1_patients 
                                                  if disease1_patients > 0 else 0)
                    prob_disease1_given_disease2 = (both_diseases / disease2_patients 
                                                  if disease2_patients > 0 else 0)
                    
                    comorbidity_stats[f"{disease1}_and_{disease2}"] = {
                        'joint_probability': prob_both,
                        'conditional_prob_1_given_2': prob_disease1_given_disease2,
                        'conditional_prob_2_given_1': prob_disease2_given_disease1,
                        'correlation': correlation_matrix.loc[disease1, disease2]
                    }
        
        return {
            'correlation_matrix': correlation_matrix,
            'comorbidity_statistics': comorbidity_stats
        }
    
    def _build_multi_disease_models(self, unified_df):
        """构建多疾病预测模型"""
        logger.info("构建多疾病预测模型")
        
        if unified_df.empty:
            return {}
        
        models = {}
        diseases = ['heart', 'stroke', 'cirrhosis']
        
        # 准备特征
        feature_cols = [col for col in unified_df.columns 
                       if col not in ['disease_type'] + [f'{d}_risk' for d in diseases]]
        
        if not feature_cols:
            logger.warning("没有可用特征用于建模")
            return {}
        
        X = unified_df[feature_cols].fillna(0)
        
        # 为每种疾病组合构建模型
        disease_combinations = [
            ('heart', 'stroke'),
            ('heart', 'cirrhosis'),
            ('stroke', 'cirrhosis'),
            ('heart', 'stroke', 'cirrhosis')
        ]
        
        for combination in disease_combinations:
            try:
                model_name = '_'.join(combination)
                logger.info(f"构建{model_name}组合模型")
                
                # 创建目标变量（任一疾病阳性）
                y_any = np.zeros(len(unified_df))
                for disease in combination:
                    risk_col = f'{disease}_risk'
                    if risk_col in unified_df.columns:
                        y_any = np.logical_or(y_any, unified_df[risk_col] == 1)
                
                # 创建目标变量（所有疾病阳性）
                y_all = np.ones(len(unified_df))
                for disease in combination:
                    risk_col = f'{disease}_risk'
                    if risk_col in unified_df.columns:
                        y_all = np.logical_and(y_all, unified_df[risk_col] == 1)
                
                if y_any.sum() == 0 or y_all.sum() == 0:
                    logger.warning(f"{model_name}组合数据不足，跳过建模")
                    continue
                
                # 训练模型
                models[model_name] = self._train_combination_model(X, y_any, y_all, model_name)
                
            except Exception as e:
                logger.error(f"构建{model_name}模型失败: {str(e)}")
                continue
        
        return models
    
    def _train_combination_model(self, X, y_any, y_all, model_name):
        """训练组合疾病模型"""
        results = {}
        
        # 分割数据
        X_train, X_test, y_any_train, y_any_test = train_test_split(
            X, y_any, test_size=0.2, random_state=42, stratify=y_any
        )
        
        # 标准化特征
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # 训练"任一疾病"模型
        rf_any = RandomForestClassifier(n_estimators=100, random_state=42)
        rf_any.fit(X_train_scaled, y_any_train)
        
        y_any_pred = rf_any.predict(X_test_scaled)
        y_any_pred_proba = rf_any.predict_proba(X_test_scaled)[:, 1]
        
        results['any_disease_model'] = {
            'model': rf_any,
            'scaler': scaler,
            'auc': roc_auc_score(y_any_test, y_any_pred_proba),
            'predictions': {
                'y_test': y_any_test,
                'y_pred': y_any_pred,
                'y_pred_proba': y_any_pred_proba
            }
        }
        
        # 如果有足够的"所有疾病"样本，训练相应模型
        if y_all.sum() >= 10:  # 至少需要10个阳性样本
            try:
                _, _, y_all_train, y_all_test = train_test_split(
                    X, y_all, test_size=0.2, random_state=42, stratify=y_all
                )
                
                rf_all = RandomForestClassifier(n_estimators=100, random_state=42)
                rf_all.fit(X_train_scaled, y_all_train)
                
                y_all_pred = rf_all.predict(X_test_scaled)
                y_all_pred_proba = rf_all.predict_proba(X_test_scaled)[:, 1]
                
                results['all_diseases_model'] = {
                    'model': rf_all,
                    'scaler': scaler,
                    'auc': roc_auc_score(y_all_test, y_all_pred_proba),
                    'predictions': {
                        'y_test': y_all_test,
                        'y_pred': y_all_pred,
                        'y_pred_proba': y_all_pred_proba
                    }
                }
            except Exception as e:
                logger.warning(f"无法训练{model_name}的'所有疾病'模型: {str(e)}")
        
        return results

    def _analyze_risk_profiles(self, unified_df):
        """分析风险分层"""
        logger.info("分析风险分层")

        if unified_df.empty:
            return {}

        risk_profiles = {}

        # 基于年龄和性别的风险分层
        if 'age_std' in unified_df.columns and 'sex_std' in unified_df.columns:
            # 年龄分组
            age_bins = pd.cut(unified_df['age_std'], bins=[0, 40, 55, 70, 100],
                            labels=['<40', '40-55', '55-70', '>70'])

            # 性别分组
            sex_labels = {0: '女性', 1: '男性'}
            unified_df['sex_label'] = unified_df['sex_std'].map(sex_labels)

            # 计算各组合的风险
            diseases = ['heart', 'stroke', 'cirrhosis']
            for disease in diseases:
                risk_col = f'{disease}_risk'
                if risk_col in unified_df.columns:
                    risk_by_age_sex = unified_df.groupby([age_bins, 'sex_label'])[risk_col].agg([
                        'mean', 'count', 'sum'
                    ]).round(4)
                    risk_profiles[f'{disease}_by_age_sex'] = risk_by_age_sex

        # 多因素风险评分
        risk_factors = []
        if 'age_std' in unified_df.columns:
            risk_factors.append((unified_df['age_std'] > 60).astype(int))
        if 'sex_std' in unified_df.columns:
            risk_factors.append(unified_df['sex_std'])  # 男性风险更高

        if risk_factors:
            total_risk_score = sum(risk_factors)
            unified_df['total_risk_score'] = total_risk_score

            # 按风险评分分层
            risk_profiles['risk_stratification'] = {}
            diseases = ['heart', 'stroke', 'cirrhosis']
            for disease in diseases:
                risk_col = f'{disease}_risk'
                if risk_col in unified_df.columns:
                    risk_by_score = unified_df.groupby('total_risk_score')[risk_col].agg([
                        'mean', 'count', 'sum'
                    ]).round(4)
                    risk_profiles['risk_stratification'][disease] = risk_by_score

        return risk_profiles

    def _analyze_comorbidity_patterns(self, unified_df):
        """分析共病模式"""
        logger.info("分析共病模式")

        if unified_df.empty:
            return {}

        diseases = ['heart', 'stroke', 'cirrhosis']
        risk_columns = [f'{disease}_risk' for disease in diseases if f'{disease}_risk' in unified_df.columns]

        if len(risk_columns) < 2:
            return {}

        patterns = {}

        # 计算各种疾病组合的患病情况
        for r in range(1, len(risk_columns) + 1):
            for combination in itertools.combinations(risk_columns, r):
                pattern_name = '_and_'.join([col.replace('_risk', '') for col in combination])

                # 计算该组合的患病人数和比例
                condition = unified_df[combination[0]] == 1
                for col in combination[1:]:
                    condition = condition & (unified_df[col] == 1)

                count = condition.sum()
                total = len(unified_df)
                proportion = count / total if total > 0 else 0

                patterns[pattern_name] = {
                    'count': int(count),
                    'total_patients': int(total),
                    'proportion': round(proportion, 4),
                    'diseases_involved': [col.replace('_risk', '') for col in combination]
                }

        # 计算最常见的共病模式
        multi_disease_patterns = {k: v for k, v in patterns.items()
                                if len(v['diseases_involved']) > 1 and v['count'] > 0}

        if multi_disease_patterns:
            most_common = max(multi_disease_patterns.items(), key=lambda x: x[1]['count'])
            patterns['most_common_comorbidity'] = {
                'pattern': most_common[0],
                'details': most_common[1]
            }

        return patterns

    def _save_multi_disease_results(self, results):
        """保存多疾病分析结果"""
        logger.info("保存多疾病分析结果")

        # 创建报告
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("多疾病关联分析报告")
        report_lines.append("=" * 80)

        # 1. 共同特征
        if 'common_features' in results:
            report_lines.append("\n【共同特征分析】")
            report_lines.append("-" * 40)
            common_features = results['common_features']
            if common_features.get('strict_common'):
                report_lines.append(f"严格共同特征: {common_features['strict_common']}")
            if common_features.get('related_features'):
                report_lines.append(f"相关特征: {common_features['related_features']}")

        # 2. 疾病相关性
        if 'disease_correlations' in results and results['disease_correlations']:
            report_lines.append("\n【疾病间相关性分析】")
            report_lines.append("-" * 40)

            correlations = results['disease_correlations']
            if 'correlation_matrix' in correlations:
                report_lines.append("疾病风险相关性矩阵:")
                corr_matrix = correlations['correlation_matrix']
                report_lines.append(str(corr_matrix.round(4)))

            if 'comorbidity_statistics' in correlations:
                report_lines.append("\n共病统计:")
                for combo, stats in correlations['comorbidity_statistics'].items():
                    report_lines.append(f"\n{combo}:")
                    report_lines.append(f"  联合概率: {stats['joint_probability']:.4f}")
                    report_lines.append(f"  相关系数: {stats['correlation']:.4f}")

        # 3. 共病模式
        if 'comorbidity_patterns' in results and results['comorbidity_patterns']:
            report_lines.append("\n【共病模式分析】")
            report_lines.append("-" * 40)

            patterns = results['comorbidity_patterns']
            for pattern_name, pattern_info in patterns.items():
                if pattern_name != 'most_common_comorbidity':
                    if isinstance(pattern_info, dict) and 'count' in pattern_info:
                        report_lines.append(f"{pattern_name}: {pattern_info['count']}人 "
                                          f"({pattern_info['proportion']:.2%})")

            if 'most_common_comorbidity' in patterns:
                most_common = patterns['most_common_comorbidity']
                report_lines.append(f"\n最常见共病模式: {most_common['pattern']}")
                report_lines.append(f"患病人数: {most_common['details']['count']}")
                report_lines.append(f"患病比例: {most_common['details']['proportion']:.2%}")

        # 4. 模型性能
        if 'multi_disease_models' in results and results['multi_disease_models']:
            report_lines.append("\n【多疾病预测模型性能】")
            report_lines.append("-" * 40)

            models = results['multi_disease_models']
            for model_name, model_info in models.items():
                report_lines.append(f"\n{model_name}模型:")
                if 'any_disease_model' in model_info:
                    auc = model_info['any_disease_model']['auc']
                    report_lines.append(f"  任一疾病预测AUC: {auc:.4f}")
                if 'all_diseases_model' in model_info:
                    auc = model_info['all_diseases_model']['auc']
                    report_lines.append(f"  所有疾病预测AUC: {auc:.4f}")

        # 保存报告
        os.makedirs('results/reports', exist_ok=True)
        with open('results/reports/multi_disease_analysis.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))

        # 保存详细数据
        if 'unified_dataset' in results and not results['unified_dataset'].empty:
            results['unified_dataset'].to_csv('results/reports/unified_dataset.csv', index=False)

        logger.info("多疾病分析结果保存完成")

    def _get_target_column(self, disease_name):
        """获取目标变量列名"""
        target_mapping = {
            'heart': 'HeartDisease',
            'stroke': 'stroke',
            'cirrhosis': 'death_risk'
        }
        return target_mapping.get(disease_name)
