#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告生成模块
Report Generation Module

负责生成分析报告和WHO建议信
"""

import pandas as pd
import numpy as np
from datetime import datetime
import logging
import os

logger = logging.getLogger(__name__)

class ReportGenerator:
    """报告生成类"""
    
    def __init__(self):
        """初始化报告生成器"""
        self.report_dir = 'results/reports'
        os.makedirs(self.report_dir, exist_ok=True)
        
    def generate_complete_report(self, processed_data, model_results, multi_disease_results):
        """生成完整的分析报告"""
        logger.info("开始生成完整分析报告")
        
        # 1. 生成执行摘要
        self.generate_executive_summary(processed_data, model_results, multi_disease_results)
        
        # 2. 生成详细技术报告
        self.generate_technical_report(processed_data, model_results, multi_disease_results)
        
        # 3. 生成WHO建议信
        self.generate_who_recommendation_letter(processed_data, model_results, multi_disease_results)
        
        # 4. 生成风险因素排序报告
        self.generate_risk_factor_ranking(model_results)
        
        logger.info("完整分析报告生成完成")
    
    def generate_executive_summary(self, processed_data, model_results, multi_disease_results):
        """生成执行摘要"""
        logger.info("生成执行摘要")
        
        summary = []
        summary.append("=" * 80)
        summary.append("疾病预测与数据分析系统 - 执行摘要")
        summary.append("Disease Prediction and Data Analysis System - Executive Summary")
        summary.append("=" * 80)
        summary.append(f"报告生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}")
        summary.append("")
        
        # 项目概述
        summary.append("【项目概述】")
        summary.append("-" * 40)
        summary.append("本项目基于世界卫生组织(WHO)的医疗质量安全改进目标，针对全球三大主要")
        summary.append("死亡原因——心血管疾病、中风和肝硬化，开发了综合性的疾病预测与")
        summary.append("数据分析系统。通过机器学习技术和统计分析方法，识别关键风险因素，")
        summary.append("构建预测模型，并提供循证医学的预防建议。")
        summary.append("")
        
        # 数据概况
        summary.append("【数据概况】")
        summary.append("-" * 40)
        total_samples = 0
        for disease_name, df in processed_data.items():
            sample_count = len(df)
            total_samples += sample_count
            target_col = self._get_target_column(disease_name)
            if target_col and target_col in df.columns:
                positive_rate = df[target_col].mean()
                summary.append(f"• {disease_name.upper()}数据集: {sample_count:,}个样本, "
                             f"阳性率: {positive_rate:.2%}")
        summary.append(f"• 总样本数: {total_samples:,}")
        summary.append("")
        
        # 主要发现
        summary.append("【主要发现】")
        summary.append("-" * 40)
        
        # 模型性能摘要
        best_models = {}
        for disease_name, disease_results in model_results.items():
            best_model_name = disease_results.get('best_model')
            if best_model_name and best_model_name in disease_results:
                auc = disease_results[best_model_name]['metrics']['auc']
                best_models[disease_name] = {'model': best_model_name, 'auc': auc}
        
        summary.append("1. 模型性能表现:")
        for disease_name, info in best_models.items():
            performance_level = self._get_performance_level(info['auc'])
            summary.append(f"   • {disease_name.upper()}: {info['model']} (AUC={info['auc']:.3f}, {performance_level})")
        summary.append("")
        
        # 关键风险因素
        summary.append("2. 关键风险因素识别:")
        for disease_name, disease_results in model_results.items():
            best_model_name = disease_results.get('best_model')
            if best_model_name and best_model_name in disease_results:
                if 'feature_importance' in disease_results[best_model_name]:
                    top_features = disease_results[best_model_name]['feature_importance'].head(3)
                    summary.append(f"   • {disease_name.upper()}前三大风险因素:")
                    for idx, row in top_features.iterrows():
                        feature_name = row['feature']
                        if 'importance' in row:
                            importance = row['importance']
                            summary.append(f"     - {feature_name} (重要性: {importance:.3f})")
                        elif 'abs_coefficient' in row:
                            coef = row['coefficient']
                            summary.append(f"     - {feature_name} (系数: {coef:.3f})")
        summary.append("")
        
        # 多疾病关联
        if multi_disease_results and 'comorbidity_patterns' in multi_disease_results:
            summary.append("3. 多疾病关联发现:")
            patterns = multi_disease_results['comorbidity_patterns']
            if 'most_common_comorbidity' in patterns:
                most_common = patterns['most_common_comorbidity']
                summary.append(f"   • 最常见共病模式: {most_common['pattern']}")
                summary.append(f"   • 共病患者比例: {most_common['details']['proportion']:.2%}")
        summary.append("")
        
        # 建议概要
        summary.append("【核心建议】")
        summary.append("-" * 40)
        summary.append("1. 建立基于人工智能的疾病早期筛查系统")
        summary.append("2. 重点关注高风险人群的多疾病联合预防")
        summary.append("3. 加强对关键风险因素的监测和干预")
        summary.append("4. 推进精准医学在疾病预防中的应用")
        summary.append("5. 建立国际合作的疾病监测网络")
        summary.append("")
        
        # 保存执行摘要
        with open(f'{self.report_dir}/executive_summary.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(summary))
        
        logger.info("执行摘要生成完成")
    
    def generate_technical_report(self, processed_data, model_results, multi_disease_results):
        """生成详细技术报告"""
        logger.info("生成详细技术报告")
        
        report = []
        report.append("=" * 100)
        report.append("疾病预测与数据分析系统 - 详细技术报告")
        report.append("Disease Prediction and Data Analysis System - Technical Report")
        report.append("=" * 100)
        report.append(f"报告生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}")
        report.append("")
        
        # 1. 方法论
        report.append("1. 方法论 (Methodology)")
        report.append("=" * 50)
        report.append("")
        report.append("1.1 数据预处理")
        report.append("• 缺失值处理: 数值型变量使用中位数填充，分类变量使用众数填充")
        report.append("• 异常值处理: 使用IQR方法识别和处理异常值")
        report.append("• 特征工程: 创建衍生特征，进行编码转换")
        report.append("• 数据标准化: 对数值型特征进行标准化处理")
        report.append("")
        
        report.append("1.2 模型构建")
        report.append("• 算法选择: 逻辑回归、随机森林、梯度提升、SVM、神经网络、XGBoost、LightGBM")
        report.append("• 数据分割: 80%训练集，20%测试集")
        report.append("• 类别平衡: 使用SMOTE技术处理类别不平衡")
        report.append("• 模型评估: 5折交叉验证，多指标综合评估")
        report.append("")
        
        report.append("1.3 评估指标")
        report.append("• 准确率 (Accuracy): 整体预测正确的比例")
        report.append("• 精确率 (Precision): 预测为阳性中真正为阳性的比例")
        report.append("• 召回率 (Recall): 所有阳性样本中被正确识别的比例")
        report.append("• F1分数: 精确率和召回率的调和平均数")
        report.append("• AUC-ROC: 受试者工作特征曲线下面积")
        report.append("")
        
        # 2. 详细结果
        report.append("2. 详细分析结果 (Detailed Results)")
        report.append("=" * 50)
        report.append("")
        
        for disease_name, disease_results in model_results.items():
            report.append(f"2.{list(model_results.keys()).index(disease_name) + 1} {disease_name.upper()}疾病分析")
            report.append("-" * 30)
            
            # 数据集信息
            if disease_name in processed_data:
                df = processed_data[disease_name]
                report.append(f"数据集规模: {len(df):,}个样本, {df.shape[1]}个特征")
                
                target_col = self._get_target_column(disease_name)
                if target_col and target_col in df.columns:
                    pos_count = df[target_col].sum()
                    neg_count = len(df) - pos_count
                    report.append(f"标签分布: 阳性 {pos_count:,} ({pos_count/len(df):.2%}), "
                                f"阴性 {neg_count:,} ({neg_count/len(df):.2%})")
            
            # 模型性能对比
            report.append("\n模型性能对比:")
            report.append(f"{'模型名称':<20} {'准确率':<8} {'精确率':<8} {'召回率':<8} {'F1分数':<8} {'AUC':<8}")
            report.append("-" * 70)
            
            for model_name, result in disease_results.items():
                if model_name != 'best_model' and 'metrics' in result:
                    metrics = result['metrics']
                    report.append(f"{model_name:<20} {metrics['accuracy']:<8.4f} "
                                f"{metrics['precision']:<8.4f} {metrics['recall']:<8.4f} "
                                f"{metrics['f1']:<8.4f} {metrics['auc']:<8.4f}")
            
            # 最佳模型详情
            best_model_name = disease_results.get('best_model')
            if best_model_name:
                report.append(f"\n最佳模型: {best_model_name}")
                best_result = disease_results[best_model_name]
                metrics = best_result['metrics']
                report.append(f"交叉验证AUC: {metrics['cv_auc_mean']:.4f} ± {metrics['cv_auc_std']:.4f}")
                
                # 特征重要性
                if 'feature_importance' in best_result:
                    importance_df = best_result['feature_importance'].head(10)
                    report.append("\n前10个重要特征:")
                    for idx, row in importance_df.iterrows():
                        feature_name = row['feature']
                        if 'importance' in row:
                            importance = row['importance']
                            report.append(f"  {idx+1:2d}. {feature_name}: {importance:.4f}")
                        elif 'abs_coefficient' in row:
                            coef = row['coefficient']
                            report.append(f"  {idx+1:2d}. {feature_name}: {coef:.4f}")
            
            report.append("")
        
        # 3. 多疾病关联分析
        if multi_disease_results:
            report.append("3. 多疾病关联分析 (Multi-Disease Analysis)")
            report.append("=" * 50)
            
            if 'disease_correlations' in multi_disease_results:
                correlations = multi_disease_results['disease_correlations']
                if 'correlation_matrix' in correlations:
                    report.append("疾病间相关性矩阵:")
                    corr_matrix = correlations['correlation_matrix']
                    report.append(str(corr_matrix.round(4)))
                    report.append("")
            
            if 'comorbidity_patterns' in multi_disease_results:
                patterns = multi_disease_results['comorbidity_patterns']
                report.append("共病模式统计:")
                for pattern_name, pattern_info in patterns.items():
                    if pattern_name != 'most_common_comorbidity' and isinstance(pattern_info, dict):
                        if 'count' in pattern_info:
                            report.append(f"• {pattern_name}: {pattern_info['count']}人 "
                                        f"({pattern_info['proportion']:.2%})")
                report.append("")
        
        # 保存技术报告
        with open(f'{self.report_dir}/technical_report.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        logger.info("详细技术报告生成完成")

    def generate_who_recommendation_letter(self, processed_data, model_results, multi_disease_results):
        """生成WHO建议信"""
        logger.info("生成WHO建议信")

        letter = []
        letter.append("世界卫生组织")
        letter.append("World Health Organization")
        letter.append("Avenue Appia 20, 1211 Geneva 27, Switzerland")
        letter.append("")
        letter.append(f"日期: {datetime.now().strftime('%Y年%m月%d日')}")
        letter.append("")
        letter.append("尊敬的世界卫生组织领导:")
        letter.append("")
        letter.append("我们谨此向贵组织提交基于大数据分析和机器学习技术的疾病预防建议报告。")
        letter.append("本研究针对全球三大主要死亡原因——心血管疾病、中风和肝硬化，通过")
        letter.append("深入的数据分析和预测建模，识别了关键风险因素，并提出了循证医学")
        letter.append("的预防策略和政策建议。")
        letter.append("")

        # 研究背景和意义
        letter.append("【研究背景】")
        letter.append("")
        letter.append("根据WHO统计数据，心血管疾病每年导致约1790万人死亡，占全球死亡")
        letter.append("人数的31%；中风是全球第二大死亡原因，约占总死亡人数的11%；")
        letter.append("肝硬化作为慢性肝病的终末期表现，同样对全球健康构成重大威胁。")
        letter.append("这三种疾病不仅造成巨大的生命损失，还给全球医疗系统带来沉重负担。")
        letter.append("")

        # 主要研究发现
        letter.append("【主要研究发现】")
        letter.append("")

        # 1. 关键风险因素
        letter.append("1. 关键风险因素识别")
        letter.append("")
        for disease_name, disease_results in model_results.items():
            disease_chinese = {'heart': '心血管疾病', 'stroke': '中风', 'cirrhosis': '肝硬化'}
            best_model_name = disease_results.get('best_model')
            if best_model_name and best_model_name in disease_results:
                letter.append(f"• {disease_chinese.get(disease_name, disease_name)}:")
                if 'feature_importance' in disease_results[best_model_name]:
                    top_features = disease_results[best_model_name]['feature_importance'].head(5)
                    for idx, row in top_features.iterrows():
                        feature_name = self._translate_feature_name(row['feature'], disease_name)
                        letter.append(f"  - {feature_name}")
                letter.append("")

        # 2. 模型预测能力
        letter.append("2. 预测模型性能")
        letter.append("")
        letter.append("我们开发的机器学习模型在疾病预测方面表现出色:")
        for disease_name, disease_results in model_results.items():
            disease_chinese = {'heart': '心血管疾病', 'stroke': '中风', 'cirrhosis': '肝硬化'}
            best_model_name = disease_results.get('best_model')
            if best_model_name and best_model_name in disease_results:
                auc = disease_results[best_model_name]['metrics']['auc']
                performance_desc = self._get_performance_description(auc)
                letter.append(f"• {disease_chinese.get(disease_name, disease_name)}: "
                            f"AUC = {auc:.3f} ({performance_desc})")
        letter.append("")

        # 3. 多疾病关联
        if multi_disease_results and 'comorbidity_patterns' in multi_disease_results:
            letter.append("3. 多疾病共病模式")
            letter.append("")
            patterns = multi_disease_results['comorbidity_patterns']
            if 'most_common_comorbidity' in patterns:
                most_common = patterns['most_common_comorbidity']
                letter.append(f"研究发现，{most_common['details']['proportion']:.1%}的患者")
                letter.append(f"存在{most_common['pattern']}的共病模式，这提示我们需要")
                letter.append("采用综合性的预防和治疗策略。")
            letter.append("")

        # 政策建议
        letter.append("【政策建议和预防措施】")
        letter.append("")

        letter.append("基于我们的研究发现，我们向WHO提出以下建议:")
        letter.append("")

        letter.append("1. 建立全球疾病早期预警系统")
        letter.append("   • 利用人工智能技术建立疾病风险评估平台")
        letter.append("   • 整合多源健康数据，实现个体化风险预测")
        letter.append("   • 建立国际间数据共享和协作机制")
        letter.append("")

        letter.append("2. 加强高风险人群筛查和干预")
        letter.append("   • 针对识别出的关键风险因素制定筛查指南")
        letter.append("   • 建立分层管理体系，重点关注高风险人群")
        letter.append("   • 推广社区级别的健康监测和干预项目")
        letter.append("")

        letter.append("3. 推进精准医学在疾病预防中的应用")
        letter.append("   • 基于个体风险特征制定个性化预防方案")
        letter.append("   • 开发智能化健康管理工具和移动应用")
        letter.append("   • 加强医疗专业人员的数据分析能力培训")
        letter.append("")

        letter.append("4. 强化多疾病综合防控策略")
        letter.append("   • 建立跨疾病的综合防控体系")
        letter.append("   • 重视共病患者的综合管理")
        letter.append("   • 促进多学科协作和整合医疗服务")
        letter.append("")

        letter.append("5. 加大健康教育和公众宣传力度")
        letter.append("   • 提高公众对疾病风险因素的认知")
        letter.append("   • 推广健康生活方式和行为干预")
        letter.append("   • 利用数字化平台扩大健康教育覆盖面")
        letter.append("")

        # 具体实施建议
        letter.append("【具体实施建议】")
        letter.append("")

        # 基于分析结果的具体建议
        priority_factors = self._get_priority_risk_factors(model_results)
        if priority_factors:
            letter.append("根据我们的分析结果，建议优先关注以下风险因素:")
            for i, factor in enumerate(priority_factors[:10], 1):
                letter.append(f"{i:2d}. {factor}")
            letter.append("")

        letter.append("我们建议WHO考虑以下具体行动:")
        letter.append("")
        letter.append("• 短期目标 (1-2年):")
        letter.append("  - 建立疾病预测模型的国际标准和指南")
        letter.append("  - 启动多国合作的疾病监测试点项目")
        letter.append("  - 制定基于AI的疾病筛查技术规范")
        letter.append("")
        letter.append("• 中期目标 (3-5年):")
        letter.append("  - 在全球范围内推广智能疾病预测系统")
        letter.append("  - 建立国际疾病风险数据库")
        letter.append("  - 培训10万名医疗专业人员掌握相关技术")
        letter.append("")
        letter.append("• 长期目标 (5-10年):")
        letter.append("  - 实现全球疾病预防的数字化转型")
        letter.append("  - 将疾病死亡率降低20%")
        letter.append("  - 建立可持续的全球健康监测网络")
        letter.append("")

        # 结语
        letter.append("【结语】")
        letter.append("")
        letter.append("疾病预防是全球健康事业的重中之重。我们相信，通过科学的数据分析")
        letter.append("和先进的预测技术，结合WHO的全球影响力和协调能力，能够有效")
        letter.append("降低这三种主要疾病的发病率和死亡率，为全人类的健康福祉做出")
        letter.append("重要贡献。")
        letter.append("")
        letter.append("我们期待与WHO建立长期合作关系，共同推进全球疾病预防事业的")
        letter.append("发展。如需进一步讨论或获取详细技术资料，请随时与我们联系。")
        letter.append("")
        letter.append("此致")
        letter.append("敬礼！")
        letter.append("")
        letter.append("疾病预测与数据分析研究团队")
        letter.append(f"{datetime.now().strftime('%Y年%m月%d日')}")
        letter.append("")
        letter.append("附件:")
        letter.append("1. 详细技术报告")
        letter.append("2. 模型性能评估结果")
        letter.append("3. 数据分析可视化图表")
        letter.append("4. 风险因素重要性排序")

        # 保存WHO建议信
        with open(f'{self.report_dir}/WHO_recommendation_letter.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(letter))

        logger.info("WHO建议信生成完成")

    def generate_risk_factor_ranking(self, model_results):
        """生成风险因素排序报告"""
        logger.info("生成风险因素排序报告")

        ranking = []
        ranking.append("=" * 80)
        ranking.append("疾病风险因素重要性排序报告")
        ranking.append("Disease Risk Factors Importance Ranking Report")
        ranking.append("=" * 80)
        ranking.append(f"报告生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}")
        ranking.append("")

        # 收集所有风险因素
        all_risk_factors = {}

        for disease_name, disease_results in model_results.items():
            best_model_name = disease_results.get('best_model')
            if best_model_name and best_model_name in disease_results:
                if 'feature_importance' in disease_results[best_model_name]:
                    importance_df = disease_results[best_model_name]['feature_importance']

                    ranking.append(f"【{disease_name.upper()}疾病风险因素排序】")
                    ranking.append("-" * 50)
                    ranking.append(f"基于{best_model_name}模型的特征重要性分析")
                    ranking.append("")
                    ranking.append(f"{'排名':<4} {'风险因素':<25} {'重要性/系数':<15} {'医学解释'}")
                    ranking.append("-" * 70)

                    for idx, row in importance_df.head(15).iterrows():
                        feature_name = row['feature']
                        translated_name = self._translate_feature_name(feature_name, disease_name)
                        medical_explanation = self._get_medical_explanation(feature_name, disease_name)

                        if 'importance' in row:
                            importance_value = f"{row['importance']:.4f}"
                        elif 'abs_coefficient' in row:
                            importance_value = f"{row['coefficient']:.4f}"
                        else:
                            importance_value = "N/A"

                        ranking.append(f"{idx+1:2d}.  {translated_name:<25} {importance_value:<15} {medical_explanation}")

                        # 收集到全局风险因素字典
                        if translated_name not in all_risk_factors:
                            all_risk_factors[translated_name] = []
                        all_risk_factors[translated_name].append({
                            'disease': disease_name,
                            'importance': importance_value,
                            'explanation': medical_explanation
                        })

                    ranking.append("")

        # 生成综合风险因素排序
        ranking.append("【综合风险因素重要性排序】")
        ranking.append("-" * 50)
        ranking.append("基于所有疾病模型的综合分析结果")
        ranking.append("")

        # 计算综合重要性（简化处理）
        comprehensive_ranking = []
        for factor, disease_list in all_risk_factors.items():
            if len(disease_list) > 1:  # 在多个疾病中都重要的因素
                comprehensive_ranking.append((factor, len(disease_list), disease_list))

        comprehensive_ranking.sort(key=lambda x: x[1], reverse=True)

        ranking.append("跨疾病重要风险因素:")
        for i, (factor, count, disease_list) in enumerate(comprehensive_ranking[:10], 1):
            diseases = [item['disease'] for item in disease_list]
            ranking.append(f"{i:2d}. {factor} (涉及{count}种疾病: {', '.join(diseases)})")

        ranking.append("")

        # 保存风险因素排序报告
        with open(f'{self.report_dir}/risk_factor_ranking.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(ranking))

        logger.info("风险因素排序报告生成完成")

    def _get_target_column(self, disease_name):
        """获取目标变量列名"""
        target_mapping = {
            'heart': 'HeartDisease',
            'stroke': 'stroke',
            'cirrhosis': 'death_risk'
        }
        return target_mapping.get(disease_name)

    def _get_performance_level(self, auc):
        """根据AUC值获取性能等级"""
        if auc >= 0.9:
            return "优秀"
        elif auc >= 0.8:
            return "良好"
        elif auc >= 0.7:
            return "一般"
        else:
            return "较差"

    def _get_performance_description(self, auc):
        """获取性能描述"""
        if auc >= 0.9:
            return "预测能力优秀"
        elif auc >= 0.8:
            return "预测能力良好"
        elif auc >= 0.7:
            return "预测能力中等"
        else:
            return "预测能力有限"

    def _translate_feature_name(self, feature_name, disease_name):
        """翻译特征名称为中文"""
        translation_dict = {
            # 通用特征
            'age': '年龄', 'Age': '年龄', 'age_std': '年龄', 'Age_years': '年龄',
            'sex_encoded': '性别', 'Sex_encoded': '性别', 'gender_encoded': '性别',

            # 心脏病特征
            'RestingBP': '静息血压', 'Cholesterol': '胆固醇', 'MaxHR': '最大心率',
            'FastingBS': '空腹血糖', 'ExerciseAngina_encoded': '运动诱发心绞痛',
            'ChestPainType_encoded': '胸痛类型', 'RestingECG_encoded': '静息心电图',
            'ST_Slope_encoded': 'ST段斜率', 'Oldpeak': 'ST段压低',

            # 中风特征
            'hypertension': '高血压', 'heart_disease': '心脏病史',
            'avg_glucose_level': '平均血糖水平', 'bmi': '体重指数',
            'smoking_encoded': '吸烟状况', 'work_type_encoded': '工作类型',
            'ever_married_encoded': '婚姻状况', 'residence_encoded': '居住类型',
            'cardiovascular_risk_score': '心血管风险评分',

            # 肝硬化特征
            'Bilirubin': '胆红素', 'Albumin': '白蛋白', 'Ascites_encoded': '腹水',
            'Hepatomegaly_encoded': '肝肿大', 'Spiders_encoded': '蜘蛛痣',
            'Edema_encoded': '水肿', 'Stage': '疾病分期', 'SGOT': '谷草转氨酶',
            'Alk_Phos': '碱性磷酸酶', 'Prothrombin': '凝血酶原时间',
            'Platelets': '血小板计数', 'Child_Pugh_Score': 'Child-Pugh评分'
        }

        return translation_dict.get(feature_name, feature_name)

    def _get_medical_explanation(self, feature_name, disease_name):
        """获取医学解释"""
        explanations = {
            # 通用解释
            'age': '年龄增长增加疾病风险',
            'Age': '年龄增长增加疾病风险',
            'age_std': '年龄增长增加疾病风险',
            'Age_years': '年龄增长增加疾病风险',
            'sex_encoded': '性别差异影响疾病易感性',
            'Sex_encoded': '性别差异影响疾病易感性',
            'gender_encoded': '性别差异影响疾病易感性',

            # 心脏病解释
            'RestingBP': '高血压是心血管疾病主要风险因素',
            'Cholesterol': '高胆固醇导致动脉粥样硬化',
            'MaxHR': '心率异常反映心脏功能状态',
            'FastingBS': '糖尿病增加心血管疾病风险',
            'ExerciseAngina_encoded': '运动时胸痛提示冠心病',
            'ChestPainType_encoded': '胸痛类型反映心脏病严重程度',

            # 中风解释
            'hypertension': '高血压是中风最重要危险因素',
            'heart_disease': '心脏病增加血栓形成风险',
            'avg_glucose_level': '高血糖损伤血管内皮',
            'bmi': '肥胖增加代谢性疾病风险',
            'smoking_encoded': '吸烟损伤血管加速动脉硬化',

            # 肝硬化解释
            'Bilirubin': '胆红素升高反映肝功能损害',
            'Albumin': '白蛋白降低提示肝合成功能下降',
            'Ascites_encoded': '腹水是肝硬化失代偿表现',
            'Stage': '疾病分期反映肝纤维化程度',
            'Child_Pugh_Score': '综合评估肝功能储备'
        }

        return explanations.get(feature_name, '影响疾病发生发展')

    def _get_priority_risk_factors(self, model_results):
        """获取优先关注的风险因素"""
        priority_factors = []

        # 收集所有重要特征
        all_features = {}
        for disease_name, disease_results in model_results.items():
            best_model_name = disease_results.get('best_model')
            if best_model_name and best_model_name in disease_results:
                if 'feature_importance' in disease_results[best_model_name]:
                    importance_df = disease_results[best_model_name]['feature_importance'].head(5)
                    for _, row in importance_df.iterrows():
                        feature_name = row['feature']
                        translated_name = self._translate_feature_name(feature_name, disease_name)
                        if translated_name not in all_features:
                            all_features[translated_name] = 0
                        all_features[translated_name] += 1

        # 按出现频次排序
        sorted_features = sorted(all_features.items(), key=lambda x: x[1], reverse=True)
        priority_factors = [feature for feature, count in sorted_features if count >= 1]

        return priority_factors
