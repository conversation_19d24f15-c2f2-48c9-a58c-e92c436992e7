#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化模块
Visualization Module

负责生成各种数据可视化图表
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from sklearn.metrics import roc_curve, precision_recall_curve, confusion_matrix
import logging
import os

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
sns.set_palette("husl")

logger = logging.getLogger(__name__)

class Visualizer:
    """可视化类"""
    
    def __init__(self):
        """初始化可视化器"""
        self.figure_dir = 'results/figures'
        os.makedirs(self.figure_dir, exist_ok=True)
        
    def create_all_visualizations(self, processed_data, model_results, multi_disease_results):
        """创建所有可视化图表"""
        logger.info("开始生成可视化图表")
        
        # 1. 数据探索性可视化
        self.create_eda_visualizations(processed_data)
        
        # 2. 模型性能可视化
        self.create_model_performance_visualizations(model_results)
        
        # 3. 特征重要性可视化
        self.create_feature_importance_visualizations(model_results)
        
        # 4. 多疾病关联可视化
        self.create_multi_disease_visualizations(processed_data, multi_disease_results)
        
        # 5. 风险因素分析可视化
        self.create_risk_factor_visualizations(processed_data)
        
        logger.info("所有可视化图表生成完成")
    
    def create_eda_visualizations(self, processed_data):
        """创建探索性数据分析可视化"""
        logger.info("生成探索性数据分析图表")
        
        for disease_name, df in processed_data.items():
            logger.info(f"为{disease_name}生成EDA图表")
            
            # 创建子图
            fig, axes = plt.subplots(2, 3, figsize=(18, 12))
            fig.suptitle(f'{disease_name.upper()}数据集探索性分析', fontsize=16, fontweight='bold')
            
            # 1. 目标变量分布
            target_col = self._get_target_column(disease_name)
            if target_col and target_col in df.columns:
                target_counts = df[target_col].value_counts()
                axes[0, 0].pie(target_counts.values, labels=target_counts.index, autopct='%1.1f%%')
                axes[0, 0].set_title(f'{target_col}分布')
            
            # 2. 年龄分布
            age_col = self._get_age_column(disease_name)
            if age_col and age_col in df.columns:
                if disease_name == 'cirrhosis' and age_col == 'Age':
                    age_data = df[age_col] / 365.25  # 转换为年
                    axes[0, 1].hist(age_data, bins=30, alpha=0.7, edgecolor='black')
                    axes[0, 1].set_xlabel('年龄（年）')
                else:
                    axes[0, 1].hist(df[age_col], bins=30, alpha=0.7, edgecolor='black')
                    axes[0, 1].set_xlabel('年龄')
                axes[0, 1].set_title('年龄分布')
                axes[0, 1].set_ylabel('频数')
            
            # 3. 性别分布
            sex_col = self._get_sex_column(disease_name)
            if sex_col and sex_col in df.columns:
                sex_counts = df[sex_col].value_counts()
                axes[0, 2].bar(sex_counts.index, sex_counts.values)
                axes[0, 2].set_title('性别分布')
                axes[0, 2].set_ylabel('人数')
            
            # 4. 相关性热图（数值型特征）
            numeric_cols = df.select_dtypes(include=[np.number]).columns[:10]  # 取前10个数值型特征
            if len(numeric_cols) > 1:
                corr_matrix = df[numeric_cols].corr()
                sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, 
                           ax=axes[1, 0], fmt='.2f')
                axes[1, 0].set_title('特征相关性热图')
            
            # 5. 箱线图（主要数值特征）
            main_numeric_features = self._get_main_numeric_features(disease_name, df)
            if main_numeric_features:
                feature = main_numeric_features[0]
                if target_col and target_col in df.columns:
                    df.boxplot(column=feature, by=target_col, ax=axes[1, 1])
                    axes[1, 1].set_title(f'{feature} vs {target_col}')
                    axes[1, 1].set_xlabel(target_col)
                else:
                    axes[1, 1].boxplot(df[feature].dropna())
                    axes[1, 1].set_title(f'{feature}分布')
            
            # 6. 缺失值热图
            missing_data = df.isnull().sum()
            if missing_data.sum() > 0:
                missing_data = missing_data[missing_data > 0].sort_values(ascending=False)
                axes[1, 2].bar(range(len(missing_data)), missing_data.values)
                axes[1, 2].set_xticks(range(len(missing_data)))
                axes[1, 2].set_xticklabels(missing_data.index, rotation=45)
                axes[1, 2].set_title('缺失值统计')
                axes[1, 2].set_ylabel('缺失数量')
            else:
                axes[1, 2].text(0.5, 0.5, '无缺失值', ha='center', va='center', 
                               transform=axes[1, 2].transAxes, fontsize=14)
                axes[1, 2].set_title('缺失值统计')
            
            plt.tight_layout()
            plt.savefig(f'{self.figure_dir}/{disease_name}_eda.png', dpi=300, bbox_inches='tight')
            plt.close()
    
    def create_model_performance_visualizations(self, model_results):
        """创建模型性能可视化"""
        logger.info("生成模型性能对比图表")
        
        for disease_name, disease_results in model_results.items():
            logger.info(f"为{disease_name}生成模型性能图表")
            
            # 收集模型性能数据
            models = []
            metrics_data = {'Model': [], 'Accuracy': [], 'Precision': [], 'Recall': [], 'F1': [], 'AUC': []}
            
            for model_name, result in disease_results.items():
                if model_name != 'best_model' and 'metrics' in result:
                    models.append(model_name)
                    metrics = result['metrics']
                    metrics_data['Model'].append(model_name)
                    metrics_data['Accuracy'].append(metrics['accuracy'])
                    metrics_data['Precision'].append(metrics['precision'])
                    metrics_data['Recall'].append(metrics['recall'])
                    metrics_data['F1'].append(metrics['f1'])
                    metrics_data['AUC'].append(metrics['auc'])
            
            if not models:
                continue
            
            # 创建性能对比图
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle(f'{disease_name.upper()}模型性能对比', fontsize=16, fontweight='bold')
            
            # 1. 各指标对比柱状图
            metrics_df = pd.DataFrame(metrics_data)
            metrics_to_plot = ['Accuracy', 'Precision', 'Recall', 'F1', 'AUC']
            
            x = np.arange(len(models))
            width = 0.15
            
            for i, metric in enumerate(metrics_to_plot):
                axes[0, 0].bar(x + i*width, metrics_df[metric], width, label=metric)
            
            axes[0, 0].set_xlabel('模型')
            axes[0, 0].set_ylabel('分数')
            axes[0, 0].set_title('模型性能指标对比')
            axes[0, 0].set_xticks(x + width * 2)
            axes[0, 0].set_xticklabels(models, rotation=45)
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)
            
            # 2. AUC对比
            axes[0, 1].bar(models, metrics_df['AUC'], color='skyblue', alpha=0.7)
            axes[0, 1].set_title('AUC对比')
            axes[0, 1].set_ylabel('AUC值')
            axes[0, 1].tick_params(axis='x', rotation=45)
            axes[0, 1].grid(True, alpha=0.3)
            
            # 3. ROC曲线（最佳模型）
            best_model_name = disease_results.get('best_model')
            if best_model_name and best_model_name in disease_results:
                predictions = disease_results[best_model_name]['predictions']
                fpr, tpr, _ = roc_curve(predictions['y_test'], predictions['y_pred_proba'])
                auc_score = disease_results[best_model_name]['metrics']['auc']
                
                axes[1, 0].plot(fpr, tpr, label=f'{best_model_name} (AUC = {auc_score:.3f})')
                axes[1, 0].plot([0, 1], [0, 1], 'k--', label='随机分类器')
                axes[1, 0].set_xlabel('假阳性率')
                axes[1, 0].set_ylabel('真阳性率')
                axes[1, 0].set_title(f'ROC曲线 - {best_model_name}')
                axes[1, 0].legend()
                axes[1, 0].grid(True, alpha=0.3)
            
            # 4. 混淆矩阵（最佳模型）
            if best_model_name and best_model_name in disease_results:
                predictions = disease_results[best_model_name]['predictions']
                cm = confusion_matrix(predictions['y_test'], predictions['y_pred'])
                sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[1, 1])
                axes[1, 1].set_title(f'混淆矩阵 - {best_model_name}')
                axes[1, 1].set_xlabel('预测标签')
                axes[1, 1].set_ylabel('真实标签')
            
            plt.tight_layout()
            plt.savefig(f'{self.figure_dir}/{disease_name}_model_performance.png', 
                       dpi=300, bbox_inches='tight')
            plt.close()
    
    def _get_target_column(self, disease_name):
        """获取目标变量列名"""
        target_mapping = {
            'heart': 'HeartDisease',
            'stroke': 'stroke',
            'cirrhosis': 'death_risk'
        }
        return target_mapping.get(disease_name)
    
    def _get_age_column(self, disease_name):
        """获取年龄列名"""
        age_mapping = {
            'heart': 'Age',
            'stroke': 'age',
            'cirrhosis': 'Age'
        }
        return age_mapping.get(disease_name)
    
    def _get_sex_column(self, disease_name):
        """获取性别列名"""
        sex_mapping = {
            'heart': 'Sex',
            'stroke': 'gender',
            'cirrhosis': 'Sex'
        }
        return sex_mapping.get(disease_name)
    
    def _get_main_numeric_features(self, disease_name, df):
        """获取主要数值特征"""
        feature_mapping = {
            'heart': ['Age', 'RestingBP', 'Cholesterol', 'MaxHR'],
            'stroke': ['age', 'avg_glucose_level', 'bmi'],
            'cirrhosis': ['Age_years', 'Bilirubin', 'Albumin', 'Prothrombin']
        }
        features = feature_mapping.get(disease_name, [])
        return [f for f in features if f in df.columns]

    def create_feature_importance_visualizations(self, model_results):
        """创建特征重要性可视化"""
        logger.info("生成特征重要性图表")

        for disease_name, disease_results in model_results.items():
            best_model_name = disease_results.get('best_model')
            if not best_model_name or best_model_name not in disease_results:
                continue

            best_result = disease_results[best_model_name]
            if 'feature_importance' not in best_result:
                continue

            importance_df = best_result['feature_importance'].head(15)  # 取前15个重要特征

            # 创建特征重要性图
            plt.figure(figsize=(12, 8))

            if 'importance' in importance_df.columns:
                # 树模型的特征重要性
                plt.barh(range(len(importance_df)), importance_df['importance'])
                plt.yticks(range(len(importance_df)), importance_df['feature'])
                plt.xlabel('特征重要性')
                plt.title(f'{disease_name.upper()} - {best_model_name}特征重要性')
            elif 'abs_coefficient' in importance_df.columns:
                # 线性模型的系数绝对值
                colors = ['red' if coef < 0 else 'blue' for coef in importance_df['coefficient']]
                plt.barh(range(len(importance_df)), importance_df['abs_coefficient'], color=colors)
                plt.yticks(range(len(importance_df)), importance_df['feature'])
                plt.xlabel('系数绝对值')
                plt.title(f'{disease_name.upper()} - {best_model_name}特征系数')

                # 添加图例
                import matplotlib.patches as mpatches
                red_patch = mpatches.Patch(color='red', label='负系数')
                blue_patch = mpatches.Patch(color='blue', label='正系数')
                plt.legend(handles=[red_patch, blue_patch])

            plt.gca().invert_yaxis()
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            plt.savefig(f'{self.figure_dir}/{disease_name}_feature_importance.png',
                       dpi=300, bbox_inches='tight')
            plt.close()

    def create_risk_factor_visualizations(self, processed_data):
        """创建风险因素分析可视化"""
        logger.info("生成风险因素分析图表")

        for disease_name, df in processed_data.items():
            target_col = self._get_target_column(disease_name)
            if not target_col or target_col not in df.columns:
                continue

            logger.info(f"为{disease_name}生成风险因素分析图表")

            # 创建风险因素分析图
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle(f'{disease_name.upper()}风险因素分析', fontsize=16, fontweight='bold')

            # 1. 年龄与疾病风险
            age_col = self._get_age_column(disease_name)
            if age_col and age_col in df.columns:
                age_data = df[age_col]
                if disease_name == 'cirrhosis' and age_col == 'Age':
                    age_data = age_data / 365.25

                # 按年龄分组计算患病率
                age_bins = pd.cut(age_data, bins=5)
                risk_by_age = df.groupby(age_bins)[target_col].mean()

                axes[0, 0].bar(range(len(risk_by_age)), risk_by_age.values)
                axes[0, 0].set_xticks(range(len(risk_by_age)))
                axes[0, 0].set_xticklabels([f'{int(interval.left)}-{int(interval.right)}'
                                          for interval in risk_by_age.index], rotation=45)
                axes[0, 0].set_title('年龄与疾病风险')
                axes[0, 0].set_xlabel('年龄组')
                axes[0, 0].set_ylabel('患病率')
                axes[0, 0].grid(True, alpha=0.3)

            # 2. 性别与疾病风险
            sex_col = self._get_sex_column(disease_name)
            if sex_col and sex_col in df.columns:
                risk_by_sex = df.groupby(sex_col)[target_col].mean()
                axes[0, 1].bar(risk_by_sex.index, risk_by_sex.values, color=['lightblue', 'lightpink'])
                axes[0, 1].set_title('性别与疾病风险')
                axes[0, 1].set_xlabel('性别')
                axes[0, 1].set_ylabel('患病率')
                axes[0, 1].grid(True, alpha=0.3)

            # 3. 疾病特异性风险因素
            self._plot_disease_specific_risks(disease_name, df, target_col, axes[1, 0])

            # 4. 多因素风险分层
            self._plot_multi_factor_risk(disease_name, df, target_col, axes[1, 1])

            plt.tight_layout()
            plt.savefig(f'{self.figure_dir}/{disease_name}_risk_factors.png',
                       dpi=300, bbox_inches='tight')
            plt.close()

    def _plot_disease_specific_risks(self, disease_name, df, target_col, ax):
        """绘制疾病特异性风险因素"""
        if disease_name == 'heart':
            # 心脏病：胸痛类型与风险
            if 'ChestPainType' in df.columns:
                risk_by_chest_pain = df.groupby('ChestPainType')[target_col].mean()
                ax.bar(risk_by_chest_pain.index, risk_by_chest_pain.values)
                ax.set_title('胸痛类型与心脏病风险')
                ax.set_xlabel('胸痛类型')
                ax.set_ylabel('患病率')

        elif disease_name == 'stroke':
            # 中风：吸烟状况与风险
            if 'smoking_status' in df.columns:
                risk_by_smoking = df.groupby('smoking_status')[target_col].mean()
                ax.bar(range(len(risk_by_smoking)), risk_by_smoking.values)
                ax.set_xticks(range(len(risk_by_smoking)))
                ax.set_xticklabels(risk_by_smoking.index, rotation=45)
                ax.set_title('吸烟状况与中风风险')
                ax.set_xlabel('吸烟状况')
                ax.set_ylabel('患病率')

        elif disease_name == 'cirrhosis':
            # 肝硬化：疾病分期与风险
            if 'Stage' in df.columns:
                risk_by_stage = df.groupby('Stage')[target_col].mean()
                ax.bar(risk_by_stage.index, risk_by_stage.values)
                ax.set_title('疾病分期与死亡风险')
                ax.set_xlabel('疾病分期')
                ax.set_ylabel('死亡率')

        ax.grid(True, alpha=0.3)

    def _plot_multi_factor_risk(self, disease_name, df, target_col, ax):
        """绘制多因素风险分层"""
        if disease_name == 'heart':
            # 心脏病：年龄+性别风险分层
            if 'Age' in df.columns and 'Sex' in df.columns:
                age_groups = pd.cut(df['Age'], bins=[0, 50, 65, 100], labels=['<50', '50-65', '>65'])
                risk_matrix = df.groupby([age_groups, 'Sex'])[target_col].mean().unstack()
                if not risk_matrix.empty:
                    sns.heatmap(risk_matrix, annot=True, fmt='.3f', cmap='Reds', ax=ax)
                    ax.set_title('年龄+性别风险分层')

        elif disease_name == 'stroke':
            # 中风：高血压+心脏病风险分层
            if 'hypertension' in df.columns and 'heart_disease' in df.columns:
                risk_matrix = df.groupby(['hypertension', 'heart_disease'])[target_col].mean().unstack()
                if not risk_matrix.empty:
                    sns.heatmap(risk_matrix, annot=True, fmt='.3f', cmap='Reds', ax=ax)
                    ax.set_title('高血压+心脏病风险分层')

        elif disease_name == 'cirrhosis':
            # 肝硬化：腹水+肝肿大风险分层
            if 'Ascites' in df.columns and 'Hepatomegaly' in df.columns:
                risk_matrix = df.groupby(['Ascites', 'Hepatomegaly'])[target_col].mean().unstack()
                if not risk_matrix.empty:
                    sns.heatmap(risk_matrix, annot=True, fmt='.3f', cmap='Reds', ax=ax)
                    ax.set_title('腹水+肝肿大风险分层')

    def create_multi_disease_visualizations(self, processed_data, multi_disease_results):
        """创建多疾病关联可视化"""
        logger.info("生成多疾病关联分析图表")

        if not multi_disease_results:
            logger.warning("无多疾病分析结果，跳过相关可视化")
            return

        # 创建多疾病关联图
        plt.figure(figsize=(15, 10))

        # 这里需要根据multi_disease_results的具体结构来实现
        # 暂时创建一个占位图
        plt.text(0.5, 0.5, '多疾病关联分析图表\n(需要根据具体分析结果实现)',
                ha='center', va='center', fontsize=16,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
        plt.title('多疾病关联分析', fontsize=18, fontweight='bold')
        plt.axis('off')

        plt.savefig(f'{self.figure_dir}/multi_disease_analysis.png',
                   dpi=300, bbox_inches='tight')
        plt.close()

        logger.info("多疾病关联可视化完成")
