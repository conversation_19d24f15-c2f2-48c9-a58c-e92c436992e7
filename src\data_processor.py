#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理模块
Data Processing Module

负责数据加载、清洗、预处理和探索性数据分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.impute import SimpleImputer
import logging
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class DataProcessor:
    """数据处理类"""
    
    def __init__(self):
        """初始化数据处理器"""
        self.scalers = {}
        self.encoders = {}
        self.datasets = {}
        self.processed_datasets = {}
        
    def load_datasets(self):
        """加载所有数据集"""
        try:
            # 尝试加载真实数据
            if os.path.exists('data/heart.csv'):
                self.datasets['heart'] = pd.read_csv('data/heart.csv')
                logger.info(f"心脏病数据集加载成功: {self.datasets['heart'].shape}")
            else:
                self.datasets['heart'] = self._generate_heart_sample_data()
                logger.info("使用生成的心脏病示例数据")
                
            if os.path.exists('data/stroke.csv'):
                self.datasets['stroke'] = pd.read_csv('data/stroke.csv')
                logger.info(f"中风数据集加载成功: {self.datasets['stroke'].shape}")
            else:
                self.datasets['stroke'] = self._generate_stroke_sample_data()
                logger.info("使用生成的中风示例数据")
                
            if os.path.exists('data/cirrhosis.csv'):
                self.datasets['cirrhosis'] = pd.read_csv('data/cirrhosis.csv')
                logger.info(f"肝硬化数据集加载成功: {self.datasets['cirrhosis'].shape}")
            else:
                self.datasets['cirrhosis'] = self._generate_cirrhosis_sample_data()
                logger.info("使用生成的肝硬化示例数据")
                
        except Exception as e:
            logger.error(f"数据加载失败: {str(e)}")
            raise
    
    def _generate_heart_sample_data(self):
        """生成心脏病示例数据 - 基于真实字段结构"""
        np.random.seed(42)
        n_samples = 1000

        # 基于附录二的字段说明生成数据
        data = {
            'Age': np.random.randint(25, 80, n_samples),
            'Sex': np.random.choice(['M', 'F'], n_samples),
            'ChestPainType': np.random.choice(['TA', 'ATA', 'NAP', 'ASY'], n_samples,
                                           p=[0.2, 0.3, 0.3, 0.2]),
            'RestingBP': np.random.normal(130, 20, n_samples).clip(80, 200),
            'Cholesterol': np.random.normal(240, 50, n_samples).clip(100, 500),
            'FastingBS': np.random.choice([0, 1], n_samples, p=[0.8, 0.2]),
            'RestingECG': np.random.choice(['Normal', 'ST', 'LVH'], n_samples,
                                         p=[0.6, 0.3, 0.1]),
            'MaxHR': np.random.randint(60, 202, n_samples),
            'ExerciseAngina': np.random.choice(['Y', 'N'], n_samples, p=[0.3, 0.7]),
            'Oldpeak': np.random.exponential(1, n_samples).clip(0, 6),
            'ST_Slope': np.random.choice(['Up', 'Flat', 'Down'], n_samples,
                                       p=[0.4, 0.4, 0.2])
        }

        # 生成目标变量（基于医学逻辑）
        risk_score = (
            (data['Age'] > 55) * 0.25 +
            (data['Sex'] == 'M') * 0.2 +
            (data['ChestPainType'] == 'TA') * 0.3 +
            (data['RestingBP'] > 140) * 0.15 +
            (data['Cholesterol'] > 240) * 0.1 +
            data['FastingBS'] * 0.1 +
            (data['ExerciseAngina'] == 'Y') * 0.2 +
            (data['Oldpeak'] > 1) * 0.15
        )
        data['HeartDisease'] = (risk_score + np.random.normal(0, 0.2, n_samples) > 0.5).astype(int)

        return pd.DataFrame(data)
    
    def _generate_stroke_sample_data(self):
        """生成中风示例数据 - 基于真实字段结构"""
        np.random.seed(42)
        n_samples = 1200

        # 基于附录一的字段说明生成数据
        data = {
            'id': range(1, n_samples + 1),
            'gender': np.random.choice(['Male', 'Female', 'Other'], n_samples,
                                     p=[0.45, 0.54, 0.01]),
            'age': np.random.randint(18, 85, n_samples),
            'hypertension': np.random.choice([0, 1], n_samples, p=[0.7, 0.3]),
            'heart_disease': np.random.choice([0, 1], n_samples, p=[0.8, 0.2]),
            'ever_married': np.random.choice(['No', 'Yes'], n_samples, p=[0.3, 0.7]),
            'work_type': np.random.choice(['children', 'Govt_job', 'Never_worked',
                                         'Private', 'Self-employed'],
                                        n_samples, p=[0.1, 0.15, 0.05, 0.5, 0.2]),
            'Residence_type': np.random.choice(['Rural', 'Urban'], n_samples, p=[0.4, 0.6]),
            'avg_glucose_level': np.random.normal(100, 30, n_samples).clip(50, 300),
            'bmi': np.random.normal(25, 5, n_samples).clip(15, 50),
            'smoking_status': np.random.choice(['formerly smoked', 'never smoked',
                                              'smokes', 'Unknown'],
                                             n_samples, p=[0.3, 0.4, 0.2, 0.1])
        }

        # 生成目标变量（基于医学风险因素）
        risk_score = (
            (data['age'] > 60) * 0.3 +
            data['hypertension'] * 0.25 +
            data['heart_disease'] * 0.2 +
            (data['avg_glucose_level'] > 120) * 0.15 +
            (data['bmi'] > 30) * 0.1 +
            (data['smoking_status'] == 'smokes') * 0.15 +
            (data['gender'] == 'Male') * 0.1
        )
        data['stroke'] = (risk_score + np.random.normal(0, 0.2, n_samples) > 0.4).astype(int)

        return pd.DataFrame(data)
    
    def _generate_cirrhosis_sample_data(self):
        """生成肝硬化示例数据 - 基于真实字段结构"""
        np.random.seed(42)
        n_samples = 800

        # 基于附录三的字段说明生成数据
        data = {
            'ID': range(1, n_samples + 1),
            'N_Days': np.random.randint(100, 4000, n_samples),  # 天数
            'Drug': np.random.choice(['D-penicillamine', 'Placebo'], n_samples),
            'Age': np.random.randint(25*365, 75*365, n_samples),  # 年龄（天）
            'Sex': np.random.choice(['M', 'F'], n_samples),
            'Ascites': np.random.choice(['N', 'Y'], n_samples, p=[0.7, 0.3]),
            'Hepatomegaly': np.random.choice(['N', 'Y'], n_samples, p=[0.6, 0.4]),
            'Spiders': np.random.choice(['N', 'Y'], n_samples, p=[0.75, 0.25]),
            'Edema': np.random.choice(['N', 'S', 'Y'], n_samples, p=[0.7, 0.1, 0.2]),
            'Bilirubin': np.random.exponential(2, n_samples).clip(0.3, 20),  # mg/dl
            'Cholesterol': np.random.normal(300, 80, n_samples).clip(100, 600),  # mg/dl
            'Albumin': np.random.normal(3.5, 0.8, n_samples).clip(1.5, 5.0),  # g/dl
            'Copper': np.random.exponential(50, n_samples).clip(10, 500),  # μg/day
            'Alk_Phos': np.random.exponential(1000, n_samples).clip(200, 5000),  # U/L
            'SGOT': np.random.exponential(100, n_samples).clip(20, 500),  # U/ml
            'Triglycerides': np.random.normal(120, 40, n_samples).clip(50, 300),  # mg/dl
            'Platelets': np.random.normal(250, 80, n_samples).clip(50, 500),  # ml/1000
            'Prothrombin': np.random.normal(11, 2, n_samples).clip(8, 18),  # seconds
            'Stage': np.random.choice([1, 2, 3, 4], n_samples, p=[0.3, 0.3, 0.25, 0.15])
        }

        # 生成目标变量（基于医学风险因素）
        age_years = data['Age'] / 365
        risk_score = (
            (age_years > 50) * 0.2 +
            (data['Ascites'] == 'Y') * 0.3 +
            (data['Hepatomegaly'] == 'Y') * 0.2 +
            (data['Bilirubin'] > 2) * 0.15 +
            (data['Albumin'] < 3) * 0.15 +
            (data['Stage'] >= 3) * 0.2 +
            (data['Edema'] == 'Y') * 0.1
        )

        # 生成状态变量
        status_prob = risk_score + np.random.normal(0, 0.15, n_samples)
        data['Status'] = np.where(status_prob > 0.7, 'D',  # Death
                                np.where(status_prob > 0.4, 'CL', 'C'))  # CL=Liver transplant, C=Censored

        return pd.DataFrame(data)
    
    def clean_data(self, df, dataset_name):
        """数据清洗"""
        logger.info(f"开始清洗{dataset_name}数据集")
        
        # 记录原始数据信息
        original_shape = df.shape
        logger.info(f"原始数据形状: {original_shape}")
        
        # 处理缺失值
        missing_info = df.isnull().sum()
        if missing_info.sum() > 0:
            logger.info(f"发现缺失值:\n{missing_info[missing_info > 0]}")
            
            # 数值型变量用中位数填充
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 0:
                imputer_num = SimpleImputer(strategy='median')
                df[numeric_cols] = imputer_num.fit_transform(df[numeric_cols])
            
            # 分类变量用众数填充
            categorical_cols = df.select_dtypes(include=['object']).columns
            if len(categorical_cols) > 0:
                imputer_cat = SimpleImputer(strategy='most_frequent')
                df[categorical_cols] = imputer_cat.fit_transform(df[categorical_cols])
        
        # 处理重复值
        duplicates = df.duplicated().sum()
        if duplicates > 0:
            logger.info(f"发现{duplicates}个重复行，已删除")
            df = df.drop_duplicates()
        
        # 处理异常值（使用IQR方法）
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            Q1 = df[col].quantile(0.25)
            Q3 = df[col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outliers = ((df[col] < lower_bound) | (df[col] > upper_bound)).sum()
            if outliers > 0:
                logger.info(f"{col}列发现{outliers}个异常值")
                # 用边界值替换异常值
                df[col] = df[col].clip(lower_bound, upper_bound)
        
        logger.info(f"清洗后数据形状: {df.shape}")
        return df
    
    def process_all_datasets(self):
        """处理所有数据集"""
        logger.info("开始处理所有数据集")
        
        # 加载数据
        self.load_datasets()
        
        # 处理每个数据集
        for name, df in self.datasets.items():
            logger.info(f"处理{name}数据集")
            
            # 数据清洗
            cleaned_df = self.clean_data(df.copy(), name)
            
            # 特征工程和编码
            processed_df = self._feature_engineering(cleaned_df, name)
            
            # 保存处理后的数据
            self.processed_datasets[name] = processed_df
            
            # 保存到文件
            os.makedirs('data/processed', exist_ok=True)
            processed_df.to_csv(f'data/processed/{name}_processed.csv', index=False)
            
            logger.info(f"{name}数据集处理完成，形状: {processed_df.shape}")
        
        # 生成数据概览报告
        self._generate_data_overview()
        
        return self.processed_datasets

    def _feature_engineering(self, df, dataset_name):
        """特征工程"""
        logger.info(f"对{dataset_name}数据集进行特征工程")

        if dataset_name == 'heart':
            # 心脏病数据集特征工程
            df = self._process_heart_features(df)
        elif dataset_name == 'stroke':
            # 中风数据集特征工程
            df = self._process_stroke_features(df)
        elif dataset_name == 'cirrhosis':
            # 肝硬化数据集特征工程
            df = self._process_cirrhosis_features(df)

        return df

    def _process_heart_features(self, df):
        """处理心脏病数据集特征"""
        # 性别编码
        if 'Sex' in df.columns:
            df['Sex_encoded'] = (df['Sex'] == 'M').astype(int)
            self.encoders['heart_sex'] = {'M': 1, 'F': 0}

        # 胸痛类型编码
        if 'ChestPainType' in df.columns:
            chest_pain_map = {'TA': 3, 'ATA': 2, 'NAP': 1, 'ASY': 0}  # 按严重程度排序
            df['ChestPainType_encoded'] = df['ChestPainType'].map(chest_pain_map)
            self.encoders['heart_chest_pain'] = chest_pain_map

        # 静息心电图编码
        if 'RestingECG' in df.columns:
            ecg_map = {'Normal': 0, 'ST': 1, 'LVH': 2}
            df['RestingECG_encoded'] = df['RestingECG'].map(ecg_map)
            self.encoders['heart_ecg'] = ecg_map

        # 运动诱发心绞痛编码
        if 'ExerciseAngina' in df.columns:
            df['ExerciseAngina_encoded'] = (df['ExerciseAngina'] == 'Y').astype(int)

        # ST段斜率编码
        if 'ST_Slope' in df.columns:
            slope_map = {'Up': 2, 'Flat': 1, 'Down': 0}  # Up最好，Down最差
            df['ST_Slope_encoded'] = df['ST_Slope'].map(slope_map)
            self.encoders['heart_slope'] = slope_map

        # 创建年龄分组
        if 'Age' in df.columns:
            df['Age_group'] = pd.cut(df['Age'], bins=[0, 40, 55, 70, 100],
                                   labels=['青年', '中年', '老年', '高龄'])

        # 创建胆固醇风险等级
        if 'Cholesterol' in df.columns:
            df['Chol_risk'] = pd.cut(df['Cholesterol'], bins=[0, 200, 240, 300, 1000],
                                   labels=['正常', '边缘高', '高', '极高'])

        # 血压分类
        if 'RestingBP' in df.columns:
            df['BP_category'] = pd.cut(df['RestingBP'], bins=[0, 120, 140, 160, 300],
                                     labels=['正常', '高血压前期', '高血压1期', '高血压2期'])

        # 最大心率风险分层（按年龄调整）
        if 'MaxHR' in df.columns and 'Age' in df.columns:
            predicted_max_hr = 220 - df['Age']
            df['HR_reserve'] = df['MaxHR'] / predicted_max_hr
            df['HR_risk'] = pd.cut(df['HR_reserve'], bins=[0, 0.6, 0.8, 1.0, 2.0],
                                 labels=['低', '中等', '正常', '高'])

        return df

    def _process_stroke_features(self, df):
        """处理中风数据集特征"""
        # 性别编码
        if 'gender' in df.columns:
            gender_map = {'Male': 1, 'Female': 0, 'Other': 2}
            df['gender_encoded'] = df['gender'].map(gender_map)
            self.encoders['stroke_gender'] = gender_map

        # 婚姻状况编码
        if 'ever_married' in df.columns:
            df['ever_married_encoded'] = (df['ever_married'] == 'Yes').astype(int)

        # 工作类型编码（按风险等级）
        if 'work_type' in df.columns:
            work_map = {
                'children': 0,
                'Never_worked': 1,
                'Govt_job': 2,
                'Self-employed': 3,
                'Private': 4
            }
            df['work_type_encoded'] = df['work_type'].map(work_map)
            self.encoders['stroke_work'] = work_map

        # 居住类型编码
        if 'Residence_type' in df.columns:
            df['residence_encoded'] = (df['Residence_type'] == 'Urban').astype(int)

        # 吸烟状况编码（按风险等级）
        if 'smoking_status' in df.columns:
            smoking_map = {'never smoked': 0, 'Unknown': 1, 'formerly smoked': 2, 'smokes': 3}
            df['smoking_encoded'] = df['smoking_status'].map(smoking_map)
            self.encoders['stroke_smoking'] = smoking_map

        # 年龄分组
        if 'age' in df.columns:
            df['age_group'] = pd.cut(df['age'], bins=[0, 30, 45, 60, 75, 100],
                                   labels=['青年', '中青年', '中年', '老年', '高龄'])

        # BMI分类
        if 'bmi' in df.columns:
            df['bmi_category'] = pd.cut(df['bmi'], bins=[0, 18.5, 25, 30, 100],
                                      labels=['偏瘦', '正常', '超重', '肥胖'])
            # BMI风险编码
            df['bmi_risk'] = pd.cut(df['bmi'], bins=[0, 18.5, 25, 30, 35, 100],
                                  labels=[1, 0, 2, 3, 4])  # 正常BMI风险最低

        # 血糖分类
        if 'avg_glucose_level' in df.columns:
            df['glucose_category'] = pd.cut(df['avg_glucose_level'],
                                          bins=[0, 100, 126, 200, 1000],
                                          labels=['正常', '糖尿病前期', '糖尿病', '严重糖尿病'])
            # 血糖风险编码
            df['glucose_risk'] = (df['avg_glucose_level'] > 126).astype(int)

        # 综合心血管风险评分
        risk_factors = 0
        if 'hypertension' in df.columns:
            risk_factors += df['hypertension']
        if 'heart_disease' in df.columns:
            risk_factors += df['heart_disease']
        if 'smoking_encoded' in df.columns:
            risk_factors += (df['smoking_encoded'] >= 2).astype(int)
        if 'glucose_risk' in df.columns:
            risk_factors += df['glucose_risk']

        df['cardiovascular_risk_score'] = risk_factors

        return df

    def _process_cirrhosis_features(self, df):
        """处理肝硬化数据集特征"""
        # 年龄转换（从天转换为年）
        if 'Age' in df.columns:
            df['Age_years'] = df['Age'] / 365.25
            df['Age_group'] = pd.cut(df['Age_years'], bins=[0, 40, 55, 70, 100],
                                   labels=['青年', '中年', '老年', '高龄'])

        # 性别编码
        if 'Sex' in df.columns:
            df['Sex_encoded'] = (df['Sex'] == 'M').astype(int)
            self.encoders['cirrhosis_sex'] = {'M': 1, 'F': 0}

        # 药物编码
        if 'Drug' in df.columns:
            df['Drug_encoded'] = (df['Drug'] == 'D-penicillamine').astype(int)

        # 症状编码
        symptom_cols = ['Ascites', 'Hepatomegaly', 'Spiders']
        for col in symptom_cols:
            if col in df.columns:
                df[f'{col}_encoded'] = (df[col] == 'Y').astype(int)

        # 水肿编码（按严重程度）
        if 'Edema' in df.columns:
            edema_map = {'N': 0, 'S': 1, 'Y': 2}  # N=无, S=轻度, Y=重度
            df['Edema_encoded'] = df['Edema'].map(edema_map)
            self.encoders['cirrhosis_edema'] = edema_map

        # 状态编码（目标变量）
        if 'Status' in df.columns:
            status_map = {'C': 0, 'CL': 1, 'D': 2}  # C=存活, CL=肝移植, D=死亡
            df['Status_encoded'] = df['Status'].map(status_map)
            # 创建二分类目标变量（死亡 vs 其他）
            df['death_risk'] = (df['Status'] == 'D').astype(int)
            # 创建不良结局变量（死亡或需要肝移植 vs 存活）
            df['poor_outcome'] = (df['Status'].isin(['D', 'CL'])).astype(int)
            self.encoders['cirrhosis_status'] = status_map

        # 肝功能指标分类
        if 'Bilirubin' in df.columns:
            df['Bilirubin_high'] = (df['Bilirubin'] > 2.0).astype(int)
            df['Bilirubin_category'] = pd.cut(df['Bilirubin'],
                                            bins=[0, 1.2, 2.0, 5.0, 100],
                                            labels=['正常', '轻度升高', '中度升高', '重度升高'])

        if 'Albumin' in df.columns:
            df['Albumin_low'] = (df['Albumin'] < 3.5).astype(int)
            df['Albumin_category'] = pd.cut(df['Albumin'],
                                          bins=[0, 2.5, 3.5, 5.0, 10],
                                          labels=['严重低下', '轻度低下', '正常', '偏高'])

        # 肝酶指标
        if 'SGOT' in df.columns:
            df['SGOT_high'] = (df['SGOT'] > 40).astype(int)  # 正常值通常<40

        if 'Alk_Phos' in df.columns:
            df['Alk_Phos_high'] = (df['Alk_Phos'] > 147).astype(int)  # 正常值通常<147

        # 凝血功能
        if 'Prothrombin' in df.columns:
            df['Prothrombin_prolonged'] = (df['Prothrombin'] > 13).astype(int)

        # 血小板计数
        if 'Platelets' in df.columns:
            df['Platelets_low'] = (df['Platelets'] < 150).astype(int)  # 血小板减少

        # Child-Pugh评分相关特征（简化版）
        child_pugh_score = 0
        if 'Bilirubin_high' in df.columns:
            child_pugh_score += df['Bilirubin_high']
        if 'Albumin_low' in df.columns:
            child_pugh_score += df['Albumin_low']
        if 'Ascites_encoded' in df.columns:
            child_pugh_score += df['Ascites_encoded']
        if 'Edema_encoded' in df.columns:
            child_pugh_score += (df['Edema_encoded'] > 0).astype(int)
        if 'Prothrombin_prolonged' in df.columns:
            child_pugh_score += df['Prothrombin_prolonged']

        df['Child_Pugh_Score'] = child_pugh_score
        df['Child_Pugh_Class'] = pd.cut(child_pugh_score, bins=[-1, 1, 3, 10],
                                      labels=['A', 'B', 'C'])

        return df

    def _generate_data_overview(self):
        """生成数据概览报告"""
        logger.info("生成数据概览报告")

        overview_report = []
        overview_report.append("=" * 60)
        overview_report.append("数据集概览报告")
        overview_report.append("=" * 60)

        for name, df in self.processed_datasets.items():
            overview_report.append(f"\n【{name.upper()}数据集】")
            overview_report.append(f"数据形状: {df.shape}")
            overview_report.append(f"特征数量: {df.shape[1]}")
            overview_report.append(f"样本数量: {df.shape[0]}")

            # 数据类型统计
            dtype_counts = df.dtypes.value_counts()
            overview_report.append("数据类型分布:")
            for dtype, count in dtype_counts.items():
                overview_report.append(f"  {dtype}: {count}个特征")

            # 缺失值统计
            missing_count = df.isnull().sum().sum()
            overview_report.append(f"缺失值总数: {missing_count}")

            # 目标变量分布（如果存在）
            target_cols = ['target', 'stroke', 'death_risk', 'status_encoded']
            for target_col in target_cols:
                if target_col in df.columns:
                    target_dist = df[target_col].value_counts()
                    overview_report.append(f"目标变量 {target_col} 分布:")
                    for value, count in target_dist.items():
                        percentage = count / len(df) * 100
                        overview_report.append(f"  {value}: {count} ({percentage:.1f}%)")
                    break

        # 保存报告
        os.makedirs('results/reports', exist_ok=True)
        with open('results/reports/data_overview.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(overview_report))

        # 打印到控制台
        for line in overview_report:
            print(line)
