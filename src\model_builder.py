#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型构建模块
Model Building Module

负责构建和评估各种机器学习模型
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import (classification_report, confusion_matrix, 
                           roc_auc_score, roc_curve, precision_recall_curve,
                           accuracy_score, precision_score, recall_score, f1_score)
from sklearn.feature_selection import SelectKBest, f_classif
import xgboost as xgb
import lightgbm as lgb
from imblearn.over_sampling import SMOTE
import joblib
import logging
import os
import matplotlib.pyplot as plt
import seaborn as sns

logger = logging.getLogger(__name__)

class ModelBuilder:
    """模型构建类"""
    
    def __init__(self):
        """初始化模型构建器"""
        self.models = {}
        self.results = {}
        self.scalers = {}
        
    def build_all_models(self, processed_datasets):
        """为所有数据集构建模型"""
        logger.info("开始构建所有疾病预测模型")
        
        all_results = {}
        
        # 心脏病模型
        if 'heart' in processed_datasets:
            logger.info("构建心脏病预测模型")
            heart_results = self._build_disease_models(
                processed_datasets['heart'], 'HeartDisease', 'heart'
            )
            all_results['heart'] = heart_results
        
        # 中风模型
        if 'stroke' in processed_datasets:
            logger.info("构建中风预测模型")
            stroke_results = self._build_disease_models(
                processed_datasets['stroke'], 'stroke', 'stroke'
            )
            all_results['stroke'] = stroke_results
        
        # 肝硬化模型
        if 'cirrhosis' in processed_datasets:
            logger.info("构建肝硬化预测模型")
            cirrhosis_results = self._build_disease_models(
                processed_datasets['cirrhosis'], 'death_risk', 'cirrhosis'
            )
            all_results['cirrhosis'] = cirrhosis_results
        
        # 保存所有结果
        self.results = all_results
        self._save_model_results()
        
        return all_results
    
    def _build_disease_models(self, df, target_col, disease_name):
        """为单个疾病构建多个模型"""
        logger.info(f"为{disease_name}构建预测模型")
        
        # 准备数据
        X, y = self._prepare_data(df, target_col, disease_name)
        
        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # 处理类别不平衡
        if y.value_counts().min() / y.value_counts().max() < 0.3:
            logger.info(f"{disease_name}数据存在类别不平衡，使用SMOTE进行平衡")
            smote = SMOTE(random_state=42)
            X_train, y_train = smote.fit_resample(X_train, y_train)
        
        # 特征标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        self.scalers[disease_name] = scaler
        
        # 定义模型
        models = {
            'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000),
            'Random Forest': RandomForestClassifier(random_state=42, n_estimators=100),
            'Gradient Boosting': GradientBoostingClassifier(random_state=42),
            'SVM': SVC(random_state=42, probability=True),
            'Neural Network': MLPClassifier(random_state=42, max_iter=1000),
            'XGBoost': xgb.XGBClassifier(random_state=42, eval_metric='logloss'),
            'LightGBM': lgb.LGBMClassifier(random_state=42, verbose=-1)
        }
        
        # 训练和评估模型
        model_results = {}
        
        for model_name, model in models.items():
            logger.info(f"训练{model_name}模型")
            
            try:
                # 选择合适的数据（标准化 vs 原始）
                if model_name in ['Logistic Regression', 'SVM', 'Neural Network']:
                    X_train_use = X_train_scaled
                    X_test_use = X_test_scaled
                else:
                    X_train_use = X_train
                    X_test_use = X_test
                
                # 训练模型
                model.fit(X_train_use, y_train)
                
                # 预测
                y_pred = model.predict(X_test_use)
                y_pred_proba = model.predict_proba(X_test_use)[:, 1]
                
                # 评估指标
                metrics = {
                    'accuracy': accuracy_score(y_test, y_pred),
                    'precision': precision_score(y_test, y_pred),
                    'recall': recall_score(y_test, y_pred),
                    'f1': f1_score(y_test, y_pred),
                    'auc': roc_auc_score(y_test, y_pred_proba)
                }
                
                # 交叉验证
                cv_scores = cross_val_score(model, X_train_use, y_train, cv=5, scoring='roc_auc')
                metrics['cv_auc_mean'] = cv_scores.mean()
                metrics['cv_auc_std'] = cv_scores.std()
                
                # 保存模型和结果
                model_results[model_name] = {
                    'model': model,
                    'metrics': metrics,
                    'predictions': {
                        'y_test': y_test,
                        'y_pred': y_pred,
                        'y_pred_proba': y_pred_proba
                    },
                    'feature_names': X.columns.tolist()
                }
                
                # 特征重要性（如果模型支持）
                if hasattr(model, 'feature_importances_'):
                    importance = pd.DataFrame({
                        'feature': X.columns,
                        'importance': model.feature_importances_
                    }).sort_values('importance', ascending=False)
                    model_results[model_name]['feature_importance'] = importance
                elif hasattr(model, 'coef_'):
                    importance = pd.DataFrame({
                        'feature': X.columns,
                        'coefficient': model.coef_[0]
                    })
                    importance['abs_coefficient'] = np.abs(importance['coefficient'])
                    importance = importance.sort_values('abs_coefficient', ascending=False)
                    model_results[model_name]['feature_importance'] = importance
                
                logger.info(f"{model_name} - AUC: {metrics['auc']:.4f}, F1: {metrics['f1']:.4f}")
                
            except Exception as e:
                logger.error(f"{model_name}模型训练失败: {str(e)}")
                continue
        
        # 选择最佳模型
        best_model_name = max(model_results.keys(), 
                            key=lambda x: model_results[x]['metrics']['auc'])
        model_results['best_model'] = best_model_name
        
        logger.info(f"{disease_name}最佳模型: {best_model_name} "
                   f"(AUC: {model_results[best_model_name]['metrics']['auc']:.4f})")
        
        # 保存最佳模型
        os.makedirs('results/models', exist_ok=True)
        joblib.dump(model_results[best_model_name]['model'], 
                   f'results/models/{disease_name}_best_model.pkl')
        joblib.dump(self.scalers[disease_name], 
                   f'results/models/{disease_name}_scaler.pkl')
        
        return model_results
    
    def _prepare_data(self, df, target_col, disease_name):
        """准备建模数据"""
        # 确保目标变量存在
        if target_col not in df.columns:
            logger.error(f"目标变量 {target_col} 不存在于 {disease_name} 数据集中")
            raise ValueError(f"目标变量 {target_col} 不存在")

        # 分离特征和目标变量
        y = df[target_col]

        # 根据疾病类型选择相关特征
        if disease_name == 'heart':
            # 心脏病相关特征
            potential_features = [
                'Age', 'Sex_encoded', 'ChestPainType_encoded', 'RestingBP',
                'Cholesterol', 'FastingBS', 'RestingECG_encoded', 'MaxHR',
                'ExerciseAngina_encoded', 'Oldpeak', 'ST_Slope_encoded'
            ]
        elif disease_name == 'stroke':
            # 中风相关特征
            potential_features = [
                'age', 'gender_encoded', 'hypertension', 'heart_disease',
                'ever_married_encoded', 'work_type_encoded', 'residence_encoded',
                'avg_glucose_level', 'bmi', 'smoking_encoded', 'cardiovascular_risk_score'
            ]
        elif disease_name == 'cirrhosis':
            # 肝硬化相关特征
            potential_features = [
                'Age_years', 'Sex_encoded', 'Drug_encoded', 'Ascites_encoded',
                'Hepatomegaly_encoded', 'Spiders_encoded', 'Edema_encoded',
                'Bilirubin', 'Cholesterol', 'Albumin', 'Copper', 'Alk_Phos',
                'SGOT', 'Triglycerides', 'Platelets', 'Prothrombin', 'Stage',
                'Child_Pugh_Score'
            ]
        else:
            # 默认选择所有数值型特征
            potential_features = df.select_dtypes(include=[np.number]).columns.tolist()

        # 只保留实际存在的特征
        available_features = [col for col in potential_features
                            if col in df.columns and col != target_col]

        # 如果没有可用特征，使用所有数值型特征
        if not available_features:
            logger.warning(f"{disease_name}没有预定义特征，使用所有数值型特征")
            numeric_features = df.select_dtypes(include=[np.number]).columns.tolist()
            exclude_cols = [target_col, 'id', 'ID', 'N_Days'] + [
                col for col in df.columns if 'group' in col.lower() or
                'category' in col.lower() or 'risk' in col.lower()
            ]
            available_features = [col for col in numeric_features if col not in exclude_cols]

        if not available_features:
            logger.error(f"{disease_name}没有可用的建模特征")
            raise ValueError(f"{disease_name}没有可用的建模特征")

        X = df[available_features]

        # 处理缺失值
        if X.isnull().sum().sum() > 0:
            logger.warning(f"{disease_name}特征中存在缺失值，使用中位数填充")
            X = X.fillna(X.median())

        logger.info(f"{disease_name}数据集特征数量: {len(available_features)}")
        logger.info(f"特征列表: {available_features}")
        logger.info(f"目标变量分布:\n{y.value_counts()}")

        return X, y

    def _save_model_results(self):
        """保存模型结果"""
        logger.info("保存模型评估结果")

        # 创建结果汇总
        summary_results = []
        summary_results.append("=" * 80)
        summary_results.append("模型评估结果汇总")
        summary_results.append("=" * 80)

        for disease_name, disease_results in self.results.items():
            summary_results.append(f"\n【{disease_name.upper()}疾病预测模型】")
            summary_results.append("-" * 50)

            # 模型性能对比表
            performance_data = []
            for model_name, result in disease_results.items():
                if model_name != 'best_model' and 'metrics' in result:
                    metrics = result['metrics']
                    performance_data.append({
                        '模型': model_name,
                        '准确率': f"{metrics['accuracy']:.4f}",
                        '精确率': f"{metrics['precision']:.4f}",
                        '召回率': f"{metrics['recall']:.4f}",
                        'F1分数': f"{metrics['f1']:.4f}",
                        'AUC': f"{metrics['auc']:.4f}",
                        'CV_AUC': f"{metrics['cv_auc_mean']:.4f}±{metrics['cv_auc_std']:.4f}"
                    })

            # 转换为DataFrame并格式化输出
            if performance_data:
                perf_df = pd.DataFrame(performance_data)
                summary_results.append("模型性能对比:")
                summary_results.append(perf_df.to_string(index=False))

                # 最佳模型
                best_model = disease_results.get('best_model', 'Unknown')
                summary_results.append(f"\n最佳模型: {best_model}")

                # 特征重要性（最佳模型）
                if best_model in disease_results and 'feature_importance' in disease_results[best_model]:
                    importance_df = disease_results[best_model]['feature_importance']
                    summary_results.append(f"\n{best_model}特征重要性 (Top 10):")
                    summary_results.append(importance_df.head(10).to_string(index=False))

        # 保存到文件
        os.makedirs('results/reports', exist_ok=True)
        with open('results/reports/model_evaluation.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(summary_results))

        # 保存详细结果到CSV
        for disease_name, disease_results in self.results.items():
            performance_data = []
            for model_name, result in disease_results.items():
                if model_name != 'best_model' and 'metrics' in result:
                    metrics = result['metrics']
                    row = {'Disease': disease_name, 'Model': model_name}
                    row.update(metrics)
                    performance_data.append(row)

            if performance_data:
                perf_df = pd.DataFrame(performance_data)
                perf_df.to_csv(f'results/reports/{disease_name}_model_performance.csv',
                             index=False)

        logger.info("模型结果保存完成")

    def hyperparameter_tuning(self, disease_name, model_name):
        """超参数调优"""
        logger.info(f"对{disease_name}的{model_name}模型进行超参数调优")

        if disease_name not in self.results:
            logger.error(f"未找到{disease_name}的模型结果")
            return None

        # 定义超参数网格
        param_grids = {
            'Random Forest': {
                'n_estimators': [50, 100, 200],
                'max_depth': [None, 10, 20, 30],
                'min_samples_split': [2, 5, 10],
                'min_samples_leaf': [1, 2, 4]
            },
            'XGBoost': {
                'n_estimators': [50, 100, 200],
                'max_depth': [3, 6, 9],
                'learning_rate': [0.01, 0.1, 0.2],
                'subsample': [0.8, 0.9, 1.0]
            },
            'Logistic Regression': {
                'C': [0.001, 0.01, 0.1, 1, 10, 100],
                'penalty': ['l1', 'l2'],
                'solver': ['liblinear', 'saga']
            }
        }

        if model_name not in param_grids:
            logger.warning(f"未定义{model_name}的超参数网格")
            return None

        try:
            # 获取原始模型和数据
            original_result = self.results[disease_name][model_name]
            model = original_result['model']

            # 重新准备数据（这里简化处理，实际应该保存原始数据）
            logger.info("超参数调优功能需要重新实现数据准备部分")
            return None

        except Exception as e:
            logger.error(f"超参数调优失败: {str(e)}")
            return None

    def generate_model_interpretation(self):
        """生成模型解释报告"""
        logger.info("生成模型解释报告")

        interpretation_report = []
        interpretation_report.append("=" * 80)
        interpretation_report.append("模型解释与特征分析报告")
        interpretation_report.append("=" * 80)

        for disease_name, disease_results in self.results.items():
            interpretation_report.append(f"\n【{disease_name.upper()}疾病风险因素分析】")
            interpretation_report.append("-" * 60)

            best_model_name = disease_results.get('best_model')
            if not best_model_name or best_model_name not in disease_results:
                continue

            best_result = disease_results[best_model_name]

            # 特征重要性分析
            if 'feature_importance' in best_result:
                importance_df = best_result['feature_importance']
                interpretation_report.append(f"\n使用{best_model_name}模型识别的主要风险因素:")

                top_features = importance_df.head(10)
                for idx, row in top_features.iterrows():
                    if 'importance' in row:
                        interpretation_report.append(
                            f"{idx+1:2d}. {row['feature']}: {row['importance']:.4f}"
                        )
                    elif 'abs_coefficient' in row:
                        interpretation_report.append(
                            f"{idx+1:2d}. {row['feature']}: {row['abs_coefficient']:.4f} "
                            f"(系数: {row['coefficient']:.4f})"
                        )

            # 模型性能解释
            metrics = best_result['metrics']
            interpretation_report.append(f"\n模型性能指标解释:")
            interpretation_report.append(f"• AUC = {metrics['auc']:.4f}: "
                                       f"{'优秀' if metrics['auc'] > 0.8 else '良好' if metrics['auc'] > 0.7 else '一般'}")
            interpretation_report.append(f"• 精确率 = {metrics['precision']:.4f}: "
                                       f"预测为阳性的样本中，真正为阳性的比例")
            interpretation_report.append(f"• 召回率 = {metrics['recall']:.4f}: "
                                       f"所有阳性样本中，被正确识别的比例")
            interpretation_report.append(f"• F1分数 = {metrics['f1']:.4f}: "
                                       f"精确率和召回率的调和平均数")

        # 保存解释报告
        with open('results/reports/model_interpretation.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(interpretation_report))

        logger.info("模型解释报告生成完成")
