# 疾病预测与数据分析系统
# Disease Prediction and Data Analysis System

## 项目概述

本项目是一个基于机器学习的疾病预测与数据分析系统，专门针对全球三大主要死亡原因——心血管疾病、中风和肝硬化进行深入分析。系统通过先进的数据处理技术、多种机器学习算法和统计分析方法，识别关键风险因素，构建高精度预测模型，并提供循证医学的预防建议。

## 主要功能

### 1. 数据预处理与探索性分析
- 自动数据清洗（缺失值、异常值、重复值处理）
- 特征工程和编码转换
- 描述性统计分析
- 数据可视化（分布图、相关性热图、箱线图等）
- 风险因素识别

### 2. 单疾病预测模型构建
- 支持多种机器学习算法：
  - 逻辑回归 (Logistic Regression)
  - 随机森林 (Random Forest)
  - 梯度提升 (Gradient Boosting)
  - 支持向量机 (SVM)
  - 神经网络 (Neural Network)
  - XGBoost
  - LightGBM
- 自动模型选择和超参数优化
- 全面的性能评估（准确率、精确率、召回率、F1分数、AUC-ROC）
- 特征重要性分析
- 交叉验证

### 3. 多疾病关联分析与综合风险评估
- 疾病间相关性分析
- 共病模式识别
- 多疾病联合预测模型
- 风险分层分析
- 综合风险评分

### 4. 可视化与报告生成
- 自动生成多种可视化图表
- 执行摘要报告
- 详细技术报告
- WHO政策建议信
- 风险因素重要性排序

## 数据集说明

### Heart Disease Dataset (heart.csv)
包含心脏病预测相关的11个特征：
- Age: 患者年龄
- Sex: 患者性别 (M/F)
- ChestPainType: 胸痛类型 (TA/ATA/NAP/ASY)
- RestingBP: 静息血压
- Cholesterol: 血清胆固醇
- FastingBS: 空腹血糖 (0/1)
- RestingECG: 静息心电图结果
- MaxHR: 最大心率
- ExerciseAngina: 运动诱发心绞痛 (Y/N)
- Oldpeak: ST段压低值
- ST_Slope: ST段斜率
- HeartDisease: 目标变量 (0/1)

### Stroke Dataset (stroke.csv)
包含中风预测相关的12个特征：
- id: 唯一标识符
- gender: 性别 (Male/Female/Other)
- age: 患者年龄
- hypertension: 高血压 (0/1)
- heart_disease: 心脏病 (0/1)
- ever_married: 婚姻状况 (Yes/No)
- work_type: 工作类型
- Residence_type: 居住类型 (Urban/Rural)
- avg_glucose_level: 平均血糖水平
- bmi: 体重指数
- smoking_status: 吸烟状况
- stroke: 目标变量 (0/1)

### Cirrhosis Dataset (cirrhosis.csv)
包含肝硬化相关的20个特征：
- ID: 唯一标识符
- N_Days: 随访天数
- Status: 患者状态 (C/CL/D)
- Drug: 药物类型
- Age: 年龄（天）
- Sex: 性别 (M/F)
- Ascites: 腹水 (Y/N)
- Hepatomegaly: 肝肿大 (Y/N)
- Spiders: 蜘蛛痣 (Y/N)
- Edema: 水肿程度 (N/S/Y)
- Bilirubin: 血清胆红素
- Cholesterol: 血清胆固醇
- Albumin: 白蛋白
- Copper: 尿铜
- Alk_Phos: 碱性磷酸酶
- SGOT: 谷草转氨酶
- Triglycerides: 甘油三酯
- Platelets: 血小板计数
- Prothrombin: 凝血酶原时间
- Stage: 疾病分期 (1-4)

## 安装和使用

### 环境要求
- Python 3.8+
- 推荐使用虚拟环境

### 安装依赖
```bash
pip install -r requirements.txt
```

### 数据准备
1. 将数据文件放置在 `data/` 目录中：
   - `data/heart.csv`
   - `data/stroke.csv`
   - `data/cirrhosis.csv`

2. 如果没有真实数据，系统会自动生成示例数据进行演示

### 运行系统

#### 方式1：运行完整分析
```bash
python main.py
```

#### 方式2：运行演示脚本
```bash
python demo.py
```

#### 方式3：分步骤运行
```python
from src.data_processor import DataProcessor
from src.model_builder import ModelBuilder
from src.multi_disease_analyzer import MultiDiseaseAnalyzer
from src.visualizer import Visualizer
from src.report_generator import ReportGenerator

# 1. 数据处理
processor = DataProcessor()
processed_data = processor.process_all_datasets()

# 2. 模型构建
builder = ModelBuilder()
model_results = builder.build_all_models(processed_data)

# 3. 多疾病分析
analyzer = MultiDiseaseAnalyzer()
multi_results = analyzer.analyze_multi_disease_risk(processed_data, model_results)

# 4. 可视化
visualizer = Visualizer()
visualizer.create_all_visualizations(processed_data, model_results, multi_results)

# 5. 报告生成
reporter = ReportGenerator()
reporter.generate_complete_report(processed_data, model_results, multi_results)
```

## 输出结果

运行完成后，系统会在 `results/` 目录中生成以下文件：

### 图表文件 (`results/figures/`)
- `heart_eda.png`: 心脏病数据探索性分析图表
- `stroke_eda.png`: 中风数据探索性分析图表
- `cirrhosis_eda.png`: 肝硬化数据探索性分析图表
- `heart_model_performance.png`: 心脏病模型性能对比图
- `stroke_model_performance.png`: 中风模型性能对比图
- `cirrhosis_model_performance.png`: 肝硬化模型性能对比图
- `heart_feature_importance.png`: 心脏病特征重要性图
- `stroke_feature_importance.png`: 中风特征重要性图
- `cirrhosis_feature_importance.png`: 肝硬化特征重要性图
- `heart_risk_factors.png`: 心脏病风险因素分析图
- `stroke_risk_factors.png`: 中风风险因素分析图
- `cirrhosis_risk_factors.png`: 肝硬化风险因素分析图
- `multi_disease_analysis.png`: 多疾病关联分析图

### 报告文件 (`results/reports/`)
- `executive_summary.txt`: 执行摘要
- `technical_report.txt`: 详细技术报告
- `WHO_recommendation_letter.txt`: WHO政策建议信
- `risk_factor_ranking.txt`: 风险因素重要性排序
- `data_overview.txt`: 数据概览报告
- `model_evaluation.txt`: 模型评估结果
- `multi_disease_analysis.txt`: 多疾病关联分析报告

### 模型文件 (`results/models/`)
- `heart_best_model.pkl`: 心脏病最佳预测模型
- `stroke_best_model.pkl`: 中风最佳预测模型
- `cirrhosis_best_model.pkl`: 肝硬化最佳预测模型
- 对应的标准化器文件

## 项目结构

```
疾病的预测与大叔分析/
├── main.py                 # 主程序入口
├── demo.py                 # 演示脚本
├── requirements.txt        # 依赖包列表
├── README.md              # 项目说明文档
├── data/                  # 数据目录
│   ├── heart.csv
│   ├── stroke.csv
│   └── cirrhosis.csv
├── src/                   # 源代码目录
│   ├── data_processor.py      # 数据处理模块
│   ├── model_builder.py       # 模型构建模块
│   ├── multi_disease_analyzer.py  # 多疾病分析模块
│   ├── visualizer.py          # 可视化模块
│   └── report_generator.py    # 报告生成模块
├── results/               # 结果输出目录
│   ├── figures/           # 图表文件
│   ├── reports/           # 报告文件
│   └── models/            # 模型文件
└── notebooks/             # Jupyter笔记本（可选）
```

## 技术特点

1. **模块化设计**: 系统采用模块化架构，各功能模块独立，便于维护和扩展
2. **自动化流程**: 从数据处理到报告生成的全流程自动化
3. **多算法支持**: 集成多种机器学习算法，自动选择最佳模型
4. **可视化丰富**: 提供多种类型的可视化图表
5. **医学导向**: 基于医学知识进行特征工程和结果解释
6. **国际化**: 支持中英文报告生成

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目采用MIT许可证。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues页面
- 邮箱：[您的邮箱]

---

**注意**: 本系统仅用于研究和教育目的，不能替代专业医疗诊断。任何医疗决策都应咨询专业医生。
