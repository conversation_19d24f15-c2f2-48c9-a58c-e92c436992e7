#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
疾病预测与数据分析系统演示脚本
Disease Prediction and Data Analysis System Demo

这个脚本演示如何使用系统进行疾病预测分析
"""

import os
import sys
import warnings
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入自定义模块
from src.data_processor import DataProcessor
from src.model_builder import ModelBuilder
from src.multi_disease_analyzer import MultiDiseaseAnalyzer
from src.visualizer import Visualizer
from src.report_generator import ReportGenerator

# 设置警告过滤
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def demo_data_processing():
    """演示数据处理功能"""
    print("=" * 60)
    print("🔍 演示数据处理功能")
    print("=" * 60)
    
    # 创建数据处理器
    processor = DataProcessor()
    
    # 处理数据
    processed_data = processor.process_all_datasets()
    
    print(f"✅ 成功处理 {len(processed_data)} 个数据集")
    for name, df in processed_data.items():
        print(f"   • {name}: {df.shape[0]} 样本, {df.shape[1]} 特征")
    
    return processed_data

def demo_model_building(processed_data):
    """演示模型构建功能"""
    print("\n" + "=" * 60)
    print("🤖 演示模型构建功能")
    print("=" * 60)
    
    # 创建模型构建器
    builder = ModelBuilder()
    
    # 构建模型
    model_results = builder.build_all_models(processed_data)
    
    print(f"✅ 成功构建 {len(model_results)} 种疾病的预测模型")
    for disease_name, results in model_results.items():
        best_model = results.get('best_model', 'Unknown')
        if best_model in results:
            auc = results[best_model]['metrics']['auc']
            print(f"   • {disease_name}: 最佳模型 {best_model} (AUC: {auc:.4f})")
    
    return model_results

def demo_multi_disease_analysis(processed_data, model_results):
    """演示多疾病分析功能"""
    print("\n" + "=" * 60)
    print("🔗 演示多疾病关联分析功能")
    print("=" * 60)
    
    # 创建多疾病分析器
    analyzer = MultiDiseaseAnalyzer()
    
    # 进行多疾病分析
    multi_results = analyzer.analyze_multi_disease_risk(processed_data, model_results)
    
    print("✅ 多疾病关联分析完成")
    if 'comorbidity_patterns' in multi_results:
        patterns = multi_results['comorbidity_patterns']
        print(f"   • 识别出 {len(patterns)} 种共病模式")
        if 'most_common_comorbidity' in patterns:
            most_common = patterns['most_common_comorbidity']
            print(f"   • 最常见共病: {most_common['pattern']}")
    
    return multi_results

def demo_visualization(processed_data, model_results, multi_results):
    """演示可视化功能"""
    print("\n" + "=" * 60)
    print("📊 演示可视化功能")
    print("=" * 60)
    
    # 创建可视化器
    visualizer = Visualizer()
    
    # 生成可视化图表
    visualizer.create_all_visualizations(processed_data, model_results, multi_results)
    
    print("✅ 可视化图表生成完成")
    print("   • 图表保存在 'results/figures' 目录中")

def demo_report_generation(processed_data, model_results, multi_results):
    """演示报告生成功能"""
    print("\n" + "=" * 60)
    print("📋 演示报告生成功能")
    print("=" * 60)
    
    # 创建报告生成器
    reporter = ReportGenerator()
    
    # 生成报告
    reporter.generate_complete_report(processed_data, model_results, multi_results)
    
    print("✅ 分析报告生成完成")
    print("   • 执行摘要: results/reports/executive_summary.txt")
    print("   • 技术报告: results/reports/technical_report.txt")
    print("   • WHO建议信: results/reports/WHO_recommendation_letter.txt")
    print("   • 风险因素排序: results/reports/risk_factor_ranking.txt")

def main():
    """主演示函数"""
    print("🏥 疾病预测与数据分析系统演示")
    print("Disease Prediction and Data Analysis System Demo")
    print("=" * 80)
    
    try:
        # 创建必要的目录
        os.makedirs('results/figures', exist_ok=True)
        os.makedirs('results/models', exist_ok=True)
        os.makedirs('results/reports', exist_ok=True)
        
        # 1. 数据处理演示
        processed_data = demo_data_processing()
        
        # 2. 模型构建演示
        model_results = demo_model_building(processed_data)
        
        # 3. 多疾病分析演示
        multi_results = demo_multi_disease_analysis(processed_data, model_results)
        
        # 4. 可视化演示
        demo_visualization(processed_data, model_results, multi_results)
        
        # 5. 报告生成演示
        demo_report_generation(processed_data, model_results, multi_results)
        
        print("\n" + "=" * 80)
        print("🎉 演示完成！")
        print("📁 所有结果文件已保存在 'results' 目录中")
        print("📊 可视化图表保存在 'results/figures' 目录中")
        print("📋 分析报告保存在 'results/reports' 目录中")
        print("🤖 训练好的模型保存在 'results/models' 目录中")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
