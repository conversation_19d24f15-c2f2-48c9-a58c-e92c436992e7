================================================================================
模型评估结果汇总
================================================================================

【HEART疾病预测模型】
--------------------------------------------------
模型性能对比:
                 模型    准确率    精确率    召回率   F1分数    AUC        CV_AUC
Logistic Regression 0.7750 0.7281 0.8557 0.7867 0.8720 0.8028±0.0234
      Random Forest 0.7850 0.7547 0.8247 0.7882 0.8946 0.8324±0.0368
  Gradient Boosting 0.7900 0.7523 0.8454 0.7961 0.8725 0.8241±0.0345
                SVM 0.7900 0.7477 0.8557 0.7981 0.8818 0.7986±0.0194
     Neural Network 0.7550 0.7182 0.8144 0.7633 0.8310 0.7937±0.0312
            XGBoost 0.7750 0.7364 0.8351 0.7826 0.8611 0.8226±0.0407
           LightGBM 0.8100 0.7864 0.8351 0.8100 0.8687 0.8266±0.0373

最佳模型: Random Forest

Random Forest特征重要性 (Top 10):
               feature  importance
                   Age    0.225799
             RestingBP    0.151047
               Oldpeak    0.128401
           Cholesterol    0.125929
                 MaxHR    0.107451
 ChestPainType_encoded    0.099960
ExerciseAngina_encoded    0.052638
           Sex_encoded    0.048872
      ST_Slope_encoded    0.032953
    RestingECG_encoded    0.026949

【STROKE疾病预测模型】
--------------------------------------------------
模型性能对比:
                 模型    准确率    精确率    召回率   F1分数    AUC        CV_AUC
Logistic Regression 0.7625 0.7558 0.6436 0.6952 0.8328 0.7767±0.0429
      Random Forest 0.7542 0.7500 0.6238 0.6811 0.8130 0.8016±0.0387
  Gradient Boosting 0.7708 0.7614 0.6634 0.7090 0.8518 0.7905±0.0380
                SVM 0.7708 0.8108 0.5941 0.6857 0.8388 0.7596±0.0393
     Neural Network 0.7333 0.7126 0.6139 0.6596 0.7539 0.7310±0.0356
            XGBoost 0.7500 0.7158 0.6733 0.6939 0.8134 0.7673±0.0415
           LightGBM 0.7417 0.7241 0.6238 0.6702 0.8285 0.7736±0.0431

最佳模型: Gradient Boosting

Gradient Boosting特征重要性 (Top 10):
                  feature  importance
                      age    0.350622
        avg_glucose_level    0.173749
                      bmi    0.148982
             hypertension    0.134788
cardiovascular_risk_score    0.084925
           gender_encoded    0.062742
          smoking_encoded    0.030537
        work_type_encoded    0.007316
        residence_encoded    0.005577
     ever_married_encoded    0.000762

【CIRRHOSIS疾病预测模型】
--------------------------------------------------
模型性能对比:
                 模型    准确率    精确率    召回率   F1分数    AUC        CV_AUC
Logistic Regression 0.7188 0.3704 0.6452 0.4706 0.8160 0.8268±0.0168
      Random Forest 0.7625 0.3939 0.4194 0.4062 0.8091 0.9382±0.0412
  Gradient Boosting 0.7562 0.4000 0.5161 0.4507 0.7899 0.8997±0.0495
                SVM 0.7750 0.4419 0.6129 0.5135 0.7763 0.9071±0.0309
     Neural Network 0.7375 0.3830 0.5806 0.4615 0.7382 0.8939±0.0226
            XGBoost 0.7812 0.4444 0.5161 0.4776 0.8060 0.9364±0.0335
           LightGBM 0.7812 0.4474 0.5484 0.4928 0.8075 0.9338±0.0244

最佳模型: Logistic Regression

Logistic Regression特征重要性 (Top 10):
             feature  coefficient  abs_coefficient
     Ascites_encoded     0.665219         0.665219
           Age_years     0.614123         0.614123
    Child_Pugh_Score     0.604767         0.604767
        Drug_encoded    -0.526487         0.526487
         Prothrombin    -0.479749         0.479749
Hepatomegaly_encoded     0.475584         0.475584
             Albumin    -0.362503         0.362503
       Triglycerides    -0.361346         0.361346
           Bilirubin     0.348421         0.348421
     Spiders_encoded    -0.345788         0.345788