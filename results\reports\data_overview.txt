============================================================
数据集概览报告
============================================================

【HEART数据集】
数据形状: (1000, 22)
特征数量: 22
样本数量: 1000
数据类型分布:
  int64: 7个特征
  object: 5个特征
  float64: 4个特征
  int32: 2个特征
  category: 1个特征
  category: 1个特征
  category: 1个特征
  category: 1个特征
缺失值总数: 0

【STROKE数据集】
数据形状: (1200, 23)
特征数量: 23
样本数量: 1200
数据类型分布:
  int64: 11个特征
  object: 5个特征
  float64: 2个特征
  int32: 1个特征
  category: 1个特征
  category: 1个特征
  category: 1个特征
  category: 1个特征
缺失值总数: 0
目标变量 stroke 分布:
  0: 695 (57.9%)
  1: 505 (42.1%)

【CIRRHOSIS数据集】
数据形状: (800, 41)
特征数量: 41
样本数量: 800
数据类型分布:
  int64: 18个特征
  float64: 10个特征
  object: 7个特征
  int32: 2个特征
  category: 1个特征
  category: 1个特征
  category: 1个特征
  category: 1个特征
缺失值总数: 0
目标变量 death_risk 分布:
  0: 644 (80.5%)
  1: 156 (19.5%)